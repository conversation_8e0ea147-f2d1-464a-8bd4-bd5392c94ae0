// Fill out your copyright notice in the Description page of Project Settings.


#include "Data/DataTableDataMag.h"



void UDataTableDataMag::LoadAllTable()
{
	GetDataTable("/Script/Engine.DataTable'/Game/WuXia/DataTable/DT_WX_CharacterSkill.DT_WX_CharacterSkill'");
	GetDataTable("/Script/Engine.DataTable'/Game/WuXia/DataTable/DT_WX_CharacterAttribute.DT_WX_CharacterAttribute'");
	GetDataTable("/Script/Engine.DataTable'/Game/WuXia/DataTable/DT_WX_CharacterSkillMove.DT_WX_CharacterSkillMove'");
}

void UDataTableDataMag::GetDataTable(const FName TableName)
{
	UDataTable* DataTable = Cast<UDataTable>(StaticLoadObject(UDataTable::StaticClass(), nullptr, *TableName.ToString()));

	if (DataTable!= nullptr)
	{
		LoadedDataTables.Add(FName(DataTable->GetName()), DataTable);
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("Load DataTable Failed!"));
	}
	
}

UDataTable* UDataTableDataMag::GetDataTableByName(const FName Name)
{
	UDataTable** currentDataTable = LoadedDataTables.Find(Name);

	if (currentDataTable != nullptr)
	{
		return *currentDataTable;
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("Get DataTable Failed!"));
		return nullptr;
	}
}

FName UDataTableDataMag::GetRowName(const FName Name)
{
	UDataTable* dataTable=GetDataTableByName(Name);
	if (dataTable!= nullptr)
	{
		TArray<FName> RowNames=dataTable->GetRowNames();
		int32 RandomIndex=FMath::RandRange(0,RowNames.Num()-1);
		return RowNames[RandomIndex];
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("Get DataTable Failed!"));
		return FName();
	}
}

// ========== 基础数据获取方法实现 ==========

bool UDataTableDataMag::HasRowData(const FName& TableName, const FName& RowName)
{
	UDataTable* DataTable = GetDataTableByName(TableName);
	if (DataTable)
	{
		return DataTable->GetRowNames().Contains(RowName);
	}
	return false;
}

// ========== 批量数据获取方法实现 ==========

TArray<FName> UDataTableDataMag::GetAllRowNames(const FName& TableName)
{
	UDataTable* DataTable = GetDataTableByName(TableName);
	if (DataTable)
	{
		return DataTable->GetRowNames();
	}
	return TArray<FName>();
}

int32 UDataTableDataMag::GetRowCount(const FName& TableName)
{
	UDataTable* DataTable = GetDataTableByName(TableName);
	if (DataTable)
	{
		return DataTable->GetRowNames().Num();
	}
	return 0;
}

// ========== 缓存和性能优化方法实现 ==========

void UDataTableDataMag::ClearDataCache(const FName& TableName)
{
	if (TableName == NAME_None)
	{
		LoadedDataTables.Empty();
		UE_LOG(LogTemp, Warning, TEXT("Cleared all data table cache"));
	}
	else
	{
		if (LoadedDataTables.Remove(TableName) > 0)
		{
			UE_LOG(LogTemp, Warning, TEXT("Cleared cache for table: %s"), *TableName.ToString());
		}
		else
		{
			UE_LOG(LogTemp, Warning, TEXT("Table not found in cache: %s"), *TableName.ToString());
		}
	}
}

bool UDataTableDataMag::ReloadDataTable(const FName& TableName)
{
	// 先从缓存中移除
	LoadedDataTables.Remove(TableName);

	// 重新加载
	GetDataTable(TableName);

	// 检查是否加载成功
	bool bSuccess = LoadedDataTables.Contains(TableName);
	if (bSuccess)
	{
		UE_LOG(LogTemp, Warning, TEXT("Successfully reloaded table: %s"), *TableName.ToString());
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("Failed to reload table: %s"), *TableName.ToString());
	}

	return bSuccess;
}

// ========== 数据验证方法实现 ==========

bool UDataTableDataMag::IsDataTableValid(const FName& TableName)
{
	UDataTable* DataTable = GetDataTableByName(TableName);
	return DataTable != nullptr && DataTable->GetRowNames().Num() > 0;
}

FString UDataTableDataMag::GetDataTableInfo(const FName& TableName)
{
	UDataTable* DataTable = GetDataTableByName(TableName);
	if (DataTable)
	{
		return FString::Printf(TEXT("Table: %s, Rows: %d, Type: %s"),
			*TableName.ToString(),
			DataTable->GetRowNames().Num(),
			*DataTable->GetRowStruct()->GetName());
	}
	return TEXT("Invalid Table");
}

// ========== 蓝图友好的接口实现 ==========

bool UDataTableDataMag::GetRowDataAsString(const FName& TableName, const FName& RowName, FString& OutData)
{
	UDataTable* DataTable = GetDataTableByName(TableName);
	if (DataTable)
	{
		// 尝试获取行数据并转换为字符串
		uint8* RowData = DataTable->FindRowUnchecked(RowName);
		if (RowData)
		{
			// 使用结构体的导出文本功能
			const UScriptStruct* TempRowStruct = DataTable->GetRowStruct();
			TempRowStruct->ExportText(OutData, RowData, RowData, nullptr, PPF_None, nullptr);
			return true;
		}
	}
	OutData = TEXT("");
	return false;
}

TArray<FString> UDataTableDataMag::GetAllRowNamesAsString(const FName& TableName)
{
	TArray<FString> StringRowNames;
	TArray<FName> RowNames = GetAllRowNames(TableName);

	for (const FName& RowName : RowNames)
	{
		StringRowNames.Add(RowName.ToString());
	}

	return StringRowNames;
}

bool UDataTableDataMag::HasRowInTable(const FName& TableName, const FName& RowName)
{
	return HasRowData(TableName, RowName);
}
