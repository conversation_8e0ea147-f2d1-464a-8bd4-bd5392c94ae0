// Fill out your copyright notice in the Description page of Project Settings.


#include "Data/DataTableDataMag.h"



void UDataTableDataMag::LoadAllTable()
{
	GetDataTable("/Script/Engine.DataTable'/Game/PW/DataTable/DT_PW_DropItem.DT_PW_DropItem'");
}

void UDataTableDataMag::GetDataTable(const FName TableName)
{
	UDataTable* DataTable = Cast<UDataTable>(StaticLoadObject(UDataTable::StaticClass(), nullptr, *TableName.ToString()));

	if (DataTable!= nullptr)
	{
		LoadedDataTables.Add(FName(DataTable->GetName()), DataTable);
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("Load DataTable Failed!"));
	}
	
}

UDataTable* UDataTableDataMag::GetDataTableByName(const FName Name)
{
	UDataTable** currentDataTable = LoadedDataTables.Find(Name);

	if (currentDataTable != nullptr)
	{
		return *currentDataTable;
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("Get DataTable Failed!"));
		return nullptr;
	}
}

FName UDataTableDataMag::GetRowName(const FName Name)
{
	UDataTable* dataTable=GetDataTableByName(Name);
	if (dataTable!= nullptr)
	{
		TArray<FName> RowNames=dataTable->GetRowNames();
		int32 RandomIndex=FMath::RandRange(0,RowNames.Num()-1);
		return RowNames[RandomIndex];
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("Get DataTable Failed!"));
		return FName();
	}
}
