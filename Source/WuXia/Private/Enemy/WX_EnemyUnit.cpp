// Fill out your copyright notice in the Description page of Project Settings.


#include "Enemy/WX_EnemyUnit.h"
#include "Character/WX_CharacterController.h"
#include "Character/WX_CharacterUnitBase.h"
#include "Component/WX_BattleMag.h"
#include "Components/TextBlock.h"
#include "Components/VerticalBox.h"
#include "Components/WidgetComponent.h"
#include "Data/DataTableDataMag.h"
#include "Enemy/WX_EnemyAIController.h"
#include "Kismet/GameplayStatics.h"
#include "UI/WX_EnemyCurrentBeAtkedSkillInfo.h"
#include "UI/WX_SkillInfoItem.h"


// Sets default values
AWX_EnemyUnit::AWX_EnemyUnit()
{
	// Set this character to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = true;

	EnemySkillInfoWidget=CreateDefaultSubobject<UWidgetComponent>(TEXT("EnemySkillInfoWidget"));
	EnemySkillInfoWidget->SetupAttachment(RootComponent);
	
}


void AWX_EnemyUnit::UpdateDataTable(UWX_EventPayload* Uwx_EventPayload)
{
	DataTableDataMag = GetGameInstance()->GetSubsystem<UDataTableDataMag>();
	if (!DataTableDataMag)
		return;
	//通过characterID获取数据表内容
	CurrentCharacterAttributeFromDataTable=DataTableDataMag->GetRowDataByName<FWX_CharacterAttribute>(TEXT("DT_WX_CharacterAttribute"),CharacterID);
	EnemySkillInfo=Cast<UWX_EnemyCurrentBeAtkedSkillInfo>(EnemySkillInfoWidget->GetWidgetClass());
	EnemySkillInfoWidget->SetVisibility(false);
}

// Called when the game starts or when spawned
void AWX_EnemyUnit::BeginPlay()
{
	Super::BeginPlay();
	OnBeginCursorOver.AddUniqueDynamic(this, &AWX_EnemyUnit::OnCursorBegin);
	OnEndCursorOver.AddUniqueDynamic(this, &AWX_EnemyUnit::OnCursorEnd);
	EventSystem->RegisterEvent<AWX_EnemyUnit>(FName("Event.Data.UpDataTable"),this,&AWX_EnemyUnit::UpdateDataTable);
}

// Called to bind functionality to input
void AWX_EnemyUnit::SetupPlayerInputComponent(UInputComponent* PlayerInputComponent)
{
	Super::SetupPlayerInputComponent(PlayerInputComponent);
}

bool AWX_EnemyUnit::CanPerformAttack(AWX_CharacterUnitBase* Target, int32 ActionPointCost)
{
	if (!Target)
		return false;

	// 检查是否有足够的行动点
	if (CurrentActionPoints < ActionPointCost)
		return false;

	// 检查目标是否有效（不是自己，还活着等）
	if (Target == this)
		return false;

	// 检查目标是否在攻击范围内
	float Distance = FVector::Dist(GetActorLocation(), Target->GetActorLocation());
	float AttackRange = 200.0f; // 可以从配置中获取

	return Distance <= AttackRange;
}

void AWX_EnemyUnit::PerformAttackAction(AWX_CharacterUnitBase* Target)
{
	if (!CanPerformAttack(Target))
		return;

	// 面向目标
	FVector Direction = (Target->GetActorLocation() - GetActorLocation()).GetSafeNormal();
	FRotator LookRotation = FRotationMatrix::MakeFromX(Direction).Rotator();
	SetActorRotation(LookRotation);

	// 这里可以播放攻击动画、特效等
	// 实际的伤害计算应该在战斗管理器中处理

	UE_LOG(LogTemp, Log, TEXT("%s 执行攻击动作，目标: %s"), *GetName(), *Target->GetName());
}

void AWX_EnemyUnit::CanReachMaxLocation(const FVector& TargetPosition)
{
	MaxReachableActionPoints = GetMaxReachableActionPoints(GetPathToPosition(TargetPosition));
	CanReachTargetLocation= PathPoints.IsValidIndex(MaxReachableActionPoints) ? PathPoints[MaxReachableActionPoints] : FVector::ZeroVector;
}

TArray<FVector> AWX_EnemyUnit::GetPathToPosition(const FVector& TargetPosition)
{
	return FindPath(GetActorLocation(), TargetPosition);
}

bool AWX_EnemyUnit::ConsumeActionPoints(int32 Points)
{
	if (CurrentActionPoints >= Points)
	{
		CurrentActionPoints -= Points;
		UE_LOG(LogTemp, Log, TEXT("%s 消耗了 %d 行动点，剩余: %d"), *GetName(), Points, CurrentActionPoints);
		return true;
	}

	UE_LOG(LogTemp, Warning, TEXT("%s 行动点不足，需要: %d，当前: %d"), *GetName(), Points, CurrentActionPoints);
	return false;
}

bool AWX_EnemyUnit::HasEnoughActionPoints(int32 RequiredPoints) const
{
	return CurrentActionPoints >= RequiredPoints;
}

FString AWX_EnemyUnit::GetStatusInfo() const
{
	return FString::Printf(TEXT("生命: %.1f, 行动点: %d/%d, 位置: %s"),
		CharacterAttribute.Health,
		CurrentActionPoints,
		CharacterAttribute.ActionPoints,
		*GetActorLocation().ToString());
}


void AWX_EnemyUnit::CombatState()
{
	EnemySkillInfoWidget->SetVisibility(true);
	//随机获取3个skillID

	for (int i=0;i<3;i++)
	{
		int32 RandomIndex = FMath::RandRange(0, CurrentCharacterAttributeFromDataTable->SkillID.Num() - 1);
		//通过skillID获取技能数据
		CharacetSkillDT=DataTableDataMag->GetRowDataByName<FCharacterSkill>(TEXT("DT_WX_CharacterSkill"),
			FName(FString::FromInt(CurrentCharacterAttributeFromDataTable->SkillID[RandomIndex])));
		CurrentSelectionSkillID.Add(CurrentCharacterAttributeFromDataTable->SkillID[RandomIndex]);
		UUserWidget* Skillwidget=CreateWidget<UUserWidget>(GetWorld(),SkillInfoItemClass);
		UWX_SkillInfoItem* SkillInfoItem=Cast<UWX_SkillInfoItem>(Skillwidget);
		SkillInfoItem->SkillName->Text=CharacetSkillDT->SkillName;

		//枚举转字符串
		FString SkillMoveNameStr;
		FString currentSkillMoveNameStr;
		for (FCharacterSkillMove SkillMove:CharacetSkillDT->SkillMove)
		{
			if (SkillMove.MoveDirection==ESkillMoveDirectionType::High)
			{
				SkillMoveNameStr = TEXT("上");
				currentSkillMoveNameStr+=SkillMoveNameStr;
			}
			else if (SkillMove.MoveDirection==ESkillMoveDirectionType::Middle)
			{
				SkillMoveNameStr = TEXT("中");
				currentSkillMoveNameStr+=SkillMoveNameStr;
			}
			else if (SkillMove.MoveDirection==ESkillMoveDirectionType::Low)
			{
				SkillMoveNameStr = TEXT("下");
				currentSkillMoveNameStr+=SkillMoveNameStr;
			}
			else if (SkillMove.MoveDirection==ESkillMoveDirectionType::Left)
			{
				SkillMoveNameStr = TEXT("左");
				currentSkillMoveNameStr+=SkillMoveNameStr;
			}
			else if (SkillMove.MoveDirection==ESkillMoveDirectionType::Right)
			{
				SkillMoveNameStr = TEXT("右");
				currentSkillMoveNameStr+=SkillMoveNameStr;
			}

			FText CurrentText=FText::FromString(currentSkillMoveNameStr);
			SkillInfoItem->SkillMove->Text=CurrentText;
			
		}
		
		
		// SkillInfoItem->SkillMoveInfoName=
		//获取SkillInfoItemClass的widget
		EnemySkillInfo=Cast<UWX_EnemyCurrentBeAtkedSkillInfo>(EnemySkillInfoWidget->GetWidget());
		EnemySkillInfo->SkillInfoVert->AddChildToVerticalBox(SkillInfoItem);
	}

	
	//skillmove随机生成5份资源
	for (int i=0;i<5;i++)
	{
		int32 RandomIndex = FMath::RandRange(0, BattleSkillMoves.Num() - 1);
		CurrentSelectionSkillMoves.Add(BattleSkillMoves[RandomIndex]);
		// 创建事件载荷
		FGameplayTag MyEventID = FGameplayTag::RequestGameplayTag(FName("Event.UI.SkillMoveCounter"));
		Payload =EventSystem->CreateEventPayload(EEventType::Combat, MyEventID);
		Payload->EventSource = this;
		EventSystem->TriggerEvent(Payload.Get());
	}
	
}

#pragma region 战斗接口
void AWX_EnemyUnit::StartTurn_Implementation()
{
	Super::StartTurn_Implementation();
	//执行行为树
	
		// 获取AI控制器
		AWX_EnemyAIController* AIController = Cast<AWX_EnemyAIController>(GetController());
		if (AIController)
		{
			// 初始化AI（恢复行动点等）
			AIController->InitializeAI();

			// 启动行为树
			AIController->StartBehaviorTree();
			
			GEngine->AddOnScreenDebugMessage(-1, 5.f, FColor::Green, FString::Printf(TEXT("AI行为树已启动")));
		}
		else
		{
			GEngine->AddOnScreenDebugMessage(-1, 5.f, FColor::Red, FString::Printf(TEXT("AI单位没有有效的AI控制器: %s"), *GetName()));
			// 如果没有AI控制器，直接跳过这个回合
			// 这里可以触发下一个角色的回合
			EventSystem->CreateEventAndPayload("Event.Battle.NextCharacter");
		}
	
}

void AWX_EnemyUnit::EndTurn_Implementation()
{
	Super::EndTurn_Implementation();
}

bool AWX_EnemyUnit::CanPerformAction_Implementation(int32 ActionCost) const
{
	return Super::CanPerformAction_Implementation(ActionCost);
}

void AWX_EnemyUnit::PerformAttack_Implementation(AActor* Target, const FGameplayTag& AttackType)
{
	Super::PerformAttack_Implementation(Target, AttackType);
}
#pragma endregion 
