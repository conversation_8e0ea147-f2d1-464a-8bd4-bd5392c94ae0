// Fill out your copyright notice in the Description page of Project Settings.


#include "UI/WX_SkillMoveCounterItem.h"

#include "Character/WX_CharacterController.h"
#include "Components/TextBlock.h"
#include "Enemy/WX_EnemyUnit.h"
#include "Kismet/GameplayStatics.h"
#include "UI/WX_UIHUD.h"

void UWX_SkillMoveCounterItem::NativeConstruct()
{
	Super::NativeConstruct();
	SkillMoveName->SetText(SkillMoveNameText);
	HUD = Cast<AWX_UIHUD>(UGameplayStatics::GetPlayerController(GetWorld(),0)->GetHUD());
	HUD->EventSystem->RegisterEventById(FGameplayTag::RequestGameplayTag(FName("Event.UI.SkillMoveCounter")),this,&UWX_SkillMoveCounterItem::UpdateCounter);
}

void UWX_SkillMoveCounterItem::UpdateCounter(UWX_EventPayload* Payload)
{
	AWX_EnemyUnit* EnemyUnit=Cast<AWX_EnemyUnit>(Payload->EventSource);

	for (ESkillMoveDirectionType i:EnemyUnit->GetCurrentSelectionSkillMoves())
	{
		
		if (SkillMoveDirectionType==i)
		{
			index++;
			Counter->SetText(FText::FromString(FString::FromInt(index)));
			break;
		}
	}

}
