// Fill out your copyright notice in the Description page of Project Settings.

#include "AI/BTTask_WX_MoveToTarget.h"
#include "BehaviorTree/BlackboardComponent.h"
#include "BehaviorTree/BehaviorTreeComponent.h"
#include "Enemy/WX_EnemyAIController.h"
#include "Enemy/WX_EnemyUnit.h"
#include "Character/WX_CharacterUnitBase.h"
#include "AIController.h"
#include "Navigation/PathFollowingComponent.h"

UBTTask_WX_MoveToTarget::UBTTask_WX_MoveToTarget()
{
	NodeName = TEXT("WX Move To Target");
	
	// 设置黑板键过滤器
	TargetActorKey.AddObjectFilter(this, GET_MEMBER_NAME_CHECKED(UBTTask_WX_MoveToTarget, TargetActorKey), AActor::StaticClass());
}

EBTNodeResult::Type UBTTask_WX_MoveToTarget::ExecuteTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory)
{
	AWX_EnemyAIController* AIController = Cast<AWX_EnemyAIController>(OwnerComp.GetAIOwner());
	if (!AIController)
	{
		UE_LOG(LogTemp, Error, TEXT("BTTask_WX_MoveToTarget: 无效的AI控制器"));
		return EBTNodeResult::Failed;
	}

	AWX_EnemyUnit* ControlledUnit = AIController->GetControlledEnemyUnit();
	if (!ControlledUnit)
	{
		UE_LOG(LogTemp, Error, TEXT("BTTask_WX_MoveToTarget: 无效的控制单位"));
		return EBTNodeResult::Failed;
	}

	// 从黑板获取目标
	UBlackboardComponent* BlackboardComp = OwnerComp.GetBlackboardComponent();
	AWX_CharacterUnitBase* Target = Cast<AWX_CharacterUnitBase>(BlackboardComp->GetValueAsObject(TargetActorKey.SelectedKeyName));
	
	if (!Target)
	{
		UE_LOG(LogTemp, Warning, TEXT("BTTask_WX_MoveToTarget: 没有找到有效目标"));
		return EBTNodeResult::Failed;
	}

	// 计算移动位置
	FVector MovePosition;
	if (bUseOptimalAttackPosition)
	{
		MovePosition = CalculateOptimalMovePosition(AIController, Target);
	}
	else
	{
		MovePosition = Target->GetActorLocation();
	}

	// 检查是否可以移动到该位置
	if (!CanMoveToPosition(ControlledUnit, MovePosition))
	{
		UE_LOG(LogTemp, Warning, TEXT("BTTask_WX_MoveToTarget: 无法移动到目标位置，行动点不足"));
		return EBTNodeResult::Failed;
	}

	// 执行移动
	FAIMoveRequest MoveRequest;
	MoveRequest.SetGoalLocation(MovePosition);
	MoveRequest.SetAcceptanceRadius(AcceptanceRadius);
	MoveRequest.SetUsePathfinding(true);

	FNavPathSharedPtr Path;
	const FPathFollowingRequestResult RequestResult = AIController->MoveTo(MoveRequest, &Path);

	if (RequestResult.Code == EPathFollowingRequestResult::RequestSuccessful)
	{
		UE_LOG(LogTemp, Log, TEXT("BTTask_WX_MoveToTarget: 开始移动到目标位置"));
		return EBTNodeResult::InProgress;
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("BTTask_WX_MoveToTarget: 移动请求失败"));
		return EBTNodeResult::Failed;
	}
}

void UBTTask_WX_MoveToTarget::OnTaskFinished(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, EBTNodeResult::Type TaskResult)
{
	AWX_EnemyAIController* AIController = Cast<AWX_EnemyAIController>(OwnerComp.GetAIOwner());
	if (AIController)
	{
		AIController->StopMovement();
		
		if (TaskResult == EBTNodeResult::Succeeded)
		{
			UE_LOG(LogTemp, Log, TEXT("BTTask_WX_MoveToTarget: 移动完成"));
			
			// 更新黑板数据
			AIController->UpdateBlackboardData();
		}
	}

	Super::OnTaskFinished(OwnerComp, NodeMemory, TaskResult);
}

FString UBTTask_WX_MoveToTarget::GetStaticDescription() const
{
	return FString::Printf(TEXT("移动到目标: %s\n接受半径: %.1f\n使用最佳攻击位置: %s"), 
		*TargetActorKey.SelectedKeyName.ToString(),
		AcceptanceRadius,
		bUseOptimalAttackPosition ? TEXT("是") : TEXT("否"));
}

FVector UBTTask_WX_MoveToTarget::CalculateOptimalMovePosition(AWX_EnemyAIController* AIController, AWX_CharacterUnitBase* Target)
{
	if (!AIController || !Target)
		return FVector::ZeroVector;

	// 使用AI控制器的方法计算最佳攻击位置
	return AIController->GetBestAttackPosition(Target);
}

bool UBTTask_WX_MoveToTarget::CanMoveToPosition(AWX_EnemyUnit* Unit, const FVector& Position)
{
	if (!Unit)
		return false;

	// 计算移动所需的行动点
	float Distance = FVector::Dist(Unit->GetActorLocation(), Position);
	int32 RequiredActionPoints = Unit->CalculateActionPointsForDistance(Distance);

	// 检查是否有足够的行动点
	return Unit->CurrentActionPoints >= RequiredActionPoints;
}
