// Fill out your copyright notice in the Description page of Project Settings.

#include "AI/BTTask_WX_AttackTarget.h"
#include "BehaviorTree/BlackboardComponent.h"
#include "BehaviorTree/BehaviorTreeComponent.h"
#include "Enemy/WX_EnemyAIController.h"
#include "Enemy/WX_EnemyUnit.h"
#include "Character/WX_CharacterUnitBase.h"
#include "Character/WX_PlayerCharacterUnit.h"
#include "Component/WX_BattleMag.h"
#include "Kismet/GameplayStatics.h"

UBTTask_WX_AttackTarget::UBTTask_WX_AttackTarget()
{
	NodeName = TEXT("WX Attack Target");
	
	// 设置黑板键过滤器
	TargetActorKey.AddObjectFilter(this, GET_MEMBER_NAME_CHECKED(UBTTask_WX_AttackTarget, TargetActorKey), AActor::StaticClass());
}

EBTNodeResult::Type UBTTask_WX_AttackTarget::ExecuteTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory)
{
	AWX_EnemyAIController* AIController = Cast<AWX_EnemyAIController>(OwnerComp.GetAIOwner());
	if (!AIController)
	{
		UE_LOG(LogTemp, Error, TEXT("BTTask_WX_AttackTarget: 无效的AI控制器"));
		return EBTNodeResult::Failed;
	}

	AWX_EnemyUnit* ControlledUnit = AIController->GetControlledEnemyUnit();
	if (!ControlledUnit)
	{
		UE_LOG(LogTemp, Error, TEXT("BTTask_WX_AttackTarget: 无效的控制单位"));
		return EBTNodeResult::Failed;
	}

	// 从黑板获取目标
	UBlackboardComponent* BlackboardComp = OwnerComp.GetBlackboardComponent();
	AWX_CharacterUnitBase* Target = Cast<AWX_CharacterUnitBase>(BlackboardComp->GetValueAsObject(TargetActorKey.SelectedKeyName));
	
	if (!Target)
	{
		UE_LOG(LogTemp, Warning, TEXT("BTTask_WX_AttackTarget: 没有找到有效目标"));
		return EBTNodeResult::Failed;
	}

	// 检查是否可以攻击目标
	if (!CanAttackTarget(ControlledUnit, Target))
	{
		UE_LOG(LogTemp, Warning, TEXT("BTTask_WX_AttackTarget: 无法攻击目标"));
		return EBTNodeResult::Failed;
	}

	// 执行攻击
	PerformAttack(ControlledUnit, Target);

	// 消耗行动点
	ControlledUnit->CurrentActionPoints -= AttackActionPointCost;
	
	// 更新黑板数据
	AIController->UpdateBlackboardData();

	UE_LOG(LogTemp, Log, TEXT("BTTask_WX_AttackTarget: 攻击执行完成"));
	return EBTNodeResult::Succeeded;
}

FString UBTTask_WX_AttackTarget::GetStaticDescription() const
{
	return FString::Printf(TEXT("攻击目标: %s\n攻击类型: %s\n攻击范围: %.1f\n行动点消耗: %d"), 
		*TargetActorKey.SelectedKeyName.ToString(),
		bIsMeleeAttack ? TEXT("近战") : TEXT("远程"),
		AttackRange,
		AttackActionPointCost);
}

bool UBTTask_WX_AttackTarget::CanAttackTarget(AWX_EnemyUnit* Attacker, AWX_CharacterUnitBase* Target)
{
	if (!Attacker || !Target)
		return false;

	// 检查目标是否还活着
	AWX_PlayerCharacterUnit* PlayerTarget = Cast<AWX_PlayerCharacterUnit>(Target);
	if (PlayerTarget && PlayerTarget->bIsDead)
		return false;

	// 检查是否有足够的行动点
	if (Attacker->CurrentActionPoints < AttackActionPointCost)
		return false;

	// 检查攻击距离
	float Distance = FVector::Dist(Attacker->GetActorLocation(), Target->GetActorLocation());
	if (Distance > AttackRange)
		return false;

	return true;
}

void UBTTask_WX_AttackTarget::PerformAttack(AWX_EnemyUnit* Attacker, AWX_CharacterUnitBase* Target)
{
	if (!Attacker || !Target)
		return;

	// 让攻击者面向目标
	FVector Direction = (Target->GetActorLocation() - Attacker->GetActorLocation()).GetSafeNormal();
	FRotator LookRotation = FRotationMatrix::MakeFromX(Direction).Rotator();
	Attacker->SetActorRotation(LookRotation);
	

	UE_LOG(LogTemp, Log, TEXT("BTTask_WX_AttackTarget: %s 攻击了 %s"), 
		*Attacker->GetName(), *Target->GetName());
}
