#include "Character/WX_CharacterController.h"

#include "EnhancedInputComponent.h"

AWX_CharacterController::AWX_CharacterController()
{
	
}

void AWX_CharacterController::BeginPlay()
{
	Super::BeginPlay();
}

void AWX_CharacterController::SetupInputComponent()
{
	Super::SetupInputComponent();

	if (UEnhancedInputComponent* EnhancedInputComponent = Cast<UEnhancedInputComponent>(InputComponent))
	{
		EnhancedInputComponent->BindAction(SelectUpDirectionAction, ETriggerEvent::Triggered, this, &AWX_CharacterController::SelectUpDirectionStarted);
		EnhancedInputComponent->BindAction(SelectDownDirectionAction, ETriggerEvent::Triggered, this, &AWX_CharacterController::SelectDownDirectionStarted);
		EnhancedInputComponent->BindAction(SelectLeftDirectionAction, ETriggerEvent::Triggered, this, &AWX_CharacterController::SelectLeftDirectionStarted);
		EnhancedInputComponent->BindAction(SelectRightDirectionAction, ETriggerEvent::Triggered, this, &AWX_CharacterController::SelectRightDirectionStarted);
		EnhancedInputComponent->BindAction(SelectMiddleDirectionAction, ETriggerEvent::Triggered, this, &AWX_CharacterController::SelectMiddleDirectionStarted);
	}
}

void AWX_CharacterController::SelectUpDirectionStarted(const FInputActionValue& InputActionValue)
{
	GEngine->AddOnScreenDebugMessage(-1, 5.f, FColor::Green, TEXT("SelectUpDirectionStarted"));
}

void AWX_CharacterController::SelectDownDirectionStarted(const FInputActionValue& InputActionValue)
{
	GEngine->AddOnScreenDebugMessage(-1, 5.f, FColor::Green, TEXT("SelectDownDirectionStarted"));
}

void AWX_CharacterController::SelectLeftDirectionStarted(const FInputActionValue& InputActionValue)
{
	GEngine->AddOnScreenDebugMessage(-1, 5.f, FColor::Green, TEXT("SelectLeftDirectionStarted"));
}

void AWX_CharacterController::SelectRightDirectionStarted(const FInputActionValue& InputActionValue)
{
	GEngine->AddOnScreenDebugMessage(-1, 5.f, FColor::Green, TEXT("SelectRightDirectionStarted"));
}

void AWX_CharacterController::SelectMiddleDirectionStarted(const FInputActionValue& InputActionValue)
{
	GEngine->AddOnScreenDebugMessage(-1, 5.f, FColor::Green, TEXT("SelectMiddleDirectionStarted"));
}


void AWX_CharacterController::ReleaseSkill()
{
}

void AWX_CharacterController::ReleaseSkillByDirection()
{
}

void AWX_CharacterController::CounterAttack()
{
}




