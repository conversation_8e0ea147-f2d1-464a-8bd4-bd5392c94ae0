// Fill out your copyright notice in the Description page of Project Settings.


#include "Core/GameModeManager.h"
#include "EngineUtils.h"
#include "Character/WX_CharacterUnitBase.h"
#include "Character/WX_PlayerCharacterUnit.h"
#include "Engine/World.h"

AGameModeManager::AGameModeManager()
{
	PrimaryActorTick.bCanEverTick = true;
	CurrentTurnIndex = 0;
	bIsCombatActive = false;
}

void AGameModeManager::BeginPlay()
{
	Super::BeginPlay();
}

void AGameModeManager::InitializeCombatCharacters()
{
    
	// 找到所有CRPG角色
	UWorld* World = GetWorld();
	if (!World) return;
    
	for (TActorIterator<AWX_PlayerCharacterUnit> ActorItr(World); ActorItr; ++ActorItr)
	{
		AWX_PlayerCharacterUnit* Character = *ActorItr;
		if (Character)
		{
			AllCombatCharacters.Add(Character);
		}
		
		if (Character->ActorHasTag("Player"))
		{
			PlayerCharacters.Add(Character);
		}
		else if (Character->ActorHasTag("AIEnemy"))
		{
			EnemyCharacters.Add(Character);
		}
	
	
	}
	
	TArray<AWX_PlayerCharacterUnit*> NewCombatCharacters;
	NewCombatCharacters.Reserve(AllCombatCharacters.Num());
	//根据速度和先手鉴定，对全员AllCombatCharacters进行攻击先后排序，并作为一个新的攻击顺序列表
	// 过滤掉空指针
	for (AWX_PlayerCharacterUnit* Char : AllCombatCharacters)
	{
		if (IsValid(Char))
		{
			NewCombatCharacters.Add(Char);
		}
	}
    
	NewCombatCharacters.Sort([](const AWX_PlayerCharacterUnit& A, const AWX_PlayerCharacterUnit& B) {
		return A.CharacterAttribute.Agility > B.CharacterAttribute.Agility;
	});
    
	AllCombatCharacters.Empty();
	AllCombatCharacters = NewCombatCharacters;
	
	
}


class AWX_PlayerCharacterUnit* AGameModeManager::GetCurrentTurnCharacter() const
{
	if (bIsCombatActive && AllCombatCharacters.IsValidIndex(CurrentTurnIndex))
	{
		return AllCombatCharacters[CurrentTurnIndex];
	}
	return nullptr;
}
