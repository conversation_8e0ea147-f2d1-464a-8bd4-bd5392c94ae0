// Fill out your copyright notice in the Description page of Project Settings.


#include "Component/WX_BattleMag.h"
#include "Character/WX_CharacterController.h"
#include "Character/WX_CharacterUnitBase.h"
#include "Character/WX_PlayerCharacterUnit.h"
#include "Enemy/WX_EnemyAIController.h"
#include "Enemy/WX_EnemyUnit.h"
#include "Event/WX_EventSystem.h"
#include "Kismet/GameplayStatics.h"
#include "Kismet/KismetSystemLibrary.h"



// Called when the game starts
void AWX_BattleMag::BeginPlay()
{
	Super::BeginPlay();
	PawnController=Cast<AWX_CharacterController>(UGameplayStatics::GetPlayerController(GetWorld(), 0));
	EventSystem = GetGameInstance()->GetSubsystem<UWX_EventSystem>();
	if (EventSystem)
	{
		EventSystem->RegisterEvent<AWX_BattleMag>(FName("Event.Battle.EnterBattle"),this,&AWX_BattleMag::EnterBattleTurn);
		EventSystem->RegisterEvent<AWX_BattleMag>(FName("Event.Battle.NextCharacter"),this,&AWX_BattleMag::NextCharacterReady);
		EventSystem->RegisterEvent<AWX_BattleMag>(FName("Event.Battle.Dead"),this,&AWX_BattleMag::RemoveCharacterFromListIfDead);
	}
}

void AWX_BattleMag::RemoveCharacterFromListIfDead(UWX_EventPayload* Uwx_EventPayload)
{
	AWX_CharacterUnitBase* DeadCharacter=Cast<AWX_CharacterUnitBase>(Uwx_EventPayload->EventSource);
	DeadCharacter->CurrentBattleIndex=CurrentCharacterIndex;
	AllBattleUnits.Remove(DeadCharacter);
}

//通过AI感知触发，对话触发
void AWX_BattleMag::EnterBattleTurn(UWX_EventPayload* Payload)
{
	//读取配置表
	FGameplayTag MyEventID = FGameplayTag::RequestGameplayTag(FName("Event.Data.UpDataTable"));
	TSoftObjectPtr<UWX_EventPayload> TempPayload =EventSystem->CreateEventPayload(EEventType::Combat, MyEventID);
	TempPayload->EventSource = this;
	EventSystem->TriggerEvent(TempPayload.Get());
	//生成一个overspherelap获取所有Units，并分类
		TArray<AActor*> OverlappedActors;
		if (PawnController)
		{
			TArray<TEnumAsByte<EObjectTypeQuery>> ObjectTypes;
			ObjectTypes.Add(UEngineTypes::ConvertToObjectType(ECollisionChannel::ECC_Pawn));
		
			UKismetSystemLibrary::SphereOverlapActors(
				this,
				PawnController->GetSelectedUnits()[0]->GetActorLocation(),
				BattleEnterRadius,
				ObjectTypes,
				ACharacter::StaticClass(),
				TArray<AActor*>(),
				OverlappedActors
			);

			if (IsEnterDebugSphere)
			{
				DrawDebugSphere(
					GetWorld(),
				PawnController->GetSelectedUnits()[0]->GetActorLocation(),
					BattleEnterRadius,
					32,                       // 球体分段数（越大越圆）
					FColor::Red,
					false,                   // 不永久显示
					 5.f,                     // 显示时间，秒
			0,
					1.0f                     // 线宽
				);
			}
		
		}

		//对OverlappedActors进行分类
		for (AActor* units:OverlappedActors)
		{
			AWX_CharacterUnitBase* Unit=Cast<AWX_CharacterUnitBase>(units);
			if (Unit)
			{
				Unit->CharacterState= ECharacterState::Combat;
				AllBattleUnits.Add(Unit);
				//简单分类，后续可以根据阵营等复杂条件分类
				if (Unit->CharacterType==ECharacterType::Player&&!BattlePlayerUnits.Contains(Unit))
				{
					BattlePlayerUnits.Add(Unit);
				}
				if (Unit->CharacterType==ECharacterType::Enemy&&!BattleEnemyUnits.Contains(Unit))
				{
					AWX_EnemyUnit* EnemyUnit=Cast<AWX_EnemyUnit>(Unit);
					EnemyUnit->CombatState();
					BattleEnemyUnits.Add(Unit);
				}
			}
		}

		GEngine->AddOnScreenDebugMessage(-1, 5.f, FColor::Green, FString::Printf(TEXT("Enter Battle Turn: %d Player Units, %d Enemy Units"), BattlePlayerUnits.Num(), BattleEnemyUnits.Num()));
		
		//根据速度对AllbattleUnits排序，快的在前面
		AllBattleUnits.Sort([](const AWX_CharacterUnitBase& A, const AWX_CharacterUnitBase& B)
		{
			//身法
			return A.CharacterAttribute.Dexterity > B.CharacterAttribute.Dexterity;
		});

		//开始对allbattleunits进行逐一递增，并判断是玩家回合还是AI回合，执行对应函数
	if (AllBattleUnits.Num()>0)
	{
		if (AllBattleUnits[CurrentCharacterIndex]->CharacterType==ECharacterType::Player)
		{
			AWX_PlayerCharacterUnit* CurrentUnit=Cast<AWX_PlayerCharacterUnit>(AllBattleUnits[CurrentCharacterIndex]);
			PawnController->TargetUnit=CurrentUnit;
			PawnController->PreviousTargetUnit=CurrentUnit;
			PlayerTurn(CurrentUnit);
		}
		else if (AllBattleUnits[CurrentCharacterIndex]->CharacterType==ECharacterType::Enemy)
		{
			AWX_EnemyUnit* CurrentUnit=Cast<AWX_EnemyUnit>(AllBattleUnits[CurrentCharacterIndex]);
			AITurn(CurrentUnit);
		}
	}
	
	Payload->EventId=FGameplayTag::RequestGameplayTag(FName("Event.Battle.BattleUI"));
	Payload->EventSource=this;
	Payload->EventType=EEventType::Combat;
	EventSystem->TriggerEvent(Payload);
		
}


void AWX_BattleMag::PlayerTurn(AWX_PlayerCharacterUnit* CurrentUnit)
{
	if (IBP_CombatBehaviorInterface* CombatUnit=Cast<IBP_CombatBehaviorInterface>(CurrentUnit))
	{
		CombatUnit->Execute_StartTurn(CurrentUnit);
	}
}

void AWX_BattleMag::AITurn(AWX_EnemyUnit* CurrentUnit)
{
	// 使用接口统一处理
	if (IBP_CombatBehaviorInterface* CombatUnit = Cast<IBP_CombatBehaviorInterface>(CurrentUnit))
	{
		if (CurrentUnit)
		{
			CombatUnit->Execute_StartTurn(CurrentUnit);
		}
		// AI会自动执行行动，然后调用EndTurn
		// 这可以通过事件系统或回调来处理
	}
}

void AWX_BattleMag::NextCharacterReady(UWX_EventPayload* Payload)
{
	PawnController->PreviousTargetUnit->IsBeenSelected=false;
	CurrentCharacterIndex++;
	if (CurrentCharacterIndex>=AllBattleUnits.Num())
	{
		CurrentCharacterIndex=0;
	}
	if (AllBattleUnits[CurrentCharacterIndex]->CharacterType==ECharacterType::Player)
	{
		//恢复行动力
		if (AllBattleUnits[CurrentCharacterIndex]->CurrentActionPoints==0)
		{
			AllBattleUnits[CurrentCharacterIndex]->CurrentActionPoints=AllBattleUnits[CurrentCharacterIndex]->CharacterAttribute.ActionPoints;
		}
		AWX_PlayerCharacterUnit* CurrentUnit=Cast<AWX_PlayerCharacterUnit>(AllBattleUnits[CurrentCharacterIndex]);
		PawnController->TargetUnit=CurrentUnit;
		PawnController->PreviousTargetUnit=CurrentUnit;
		PlayerTurn(CurrentUnit);
	}
	else if (AllBattleUnits[CurrentCharacterIndex]->CharacterType==ECharacterType::Enemy)
	{
		AWX_EnemyUnit* CurrentUnit=Cast<AWX_EnemyUnit>(AllBattleUnits[CurrentCharacterIndex]);
		AITurn(CurrentUnit);
		NextCharacterReady(Payload);
	}
}

void AWX_BattleMag::CheckBattleSkillMove(ESkillMoveDirectionType PlayerSkillMove,
	ESkillMoveDirectionType EnemySkillMove)
{
	//招式克制逻辑 上克下,下克中,中克上，左克右,右克左，（左右可以理解为光暗互克机制）
	// 创建克制关系映射表
	TMap<TPair<ESkillMoveDirectionType, ESkillMoveDirectionType>, ESkillMoveResult> CounterTable;
    
	// 初始化克制关系表（只需要初始化一次）
	if (CounterTable.Num() == 0)
	{
		InitializeCounterTable(CounterTable);
	}
    
	// 查找克制结果
	TPair<ESkillMoveDirectionType, ESkillMoveDirectionType> MovePair(PlayerSkillMove, EnemySkillMove);
	ESkillMoveResult Result = CounterTable.FindRef(MovePair);
    
	// 处理结果
	ProcessMoveResult(Result, PlayerSkillMove, EnemySkillMove);
		
}

void AWX_BattleMag::InitializeCounterTable(TMap<TPair<ESkillMoveDirectionType, ESkillMoveDirectionType>, ESkillMoveResult>& CounterTable)
{
    // 武侠克制逻辑：上克中，中克下，下克上，左右互克
    
    // 玩家上路 vs 敌人各方向
    CounterTable.Add(MakeTuple(ESkillMoveDirectionType::High, ESkillMoveDirectionType::High), ESkillMoveResult::Draw);
    CounterTable.Add(MakeTuple(ESkillMoveDirectionType::High, ESkillMoveDirectionType::Middle), ESkillMoveResult::PlayerWin);
    CounterTable.Add(MakeTuple(ESkillMoveDirectionType::High, ESkillMoveDirectionType::Low), ESkillMoveResult::EnemyWin);
    CounterTable.Add(MakeTuple(ESkillMoveDirectionType::High, ESkillMoveDirectionType::Left), ESkillMoveResult::PlayerAdvantage);
    CounterTable.Add(MakeTuple(ESkillMoveDirectionType::High, ESkillMoveDirectionType::Right), ESkillMoveResult::PlayerAdvantage);
    
    // 玩家中路 vs 敌人各方向
    CounterTable.Add(MakeTuple(ESkillMoveDirectionType::Middle, ESkillMoveDirectionType::High), ESkillMoveResult::EnemyWin);
    CounterTable.Add(MakeTuple(ESkillMoveDirectionType::Middle, ESkillMoveDirectionType::Middle), ESkillMoveResult::Draw);
    CounterTable.Add(MakeTuple(ESkillMoveDirectionType::Middle, ESkillMoveDirectionType::Low), ESkillMoveResult::PlayerWin);
    CounterTable.Add(MakeTuple(ESkillMoveDirectionType::Middle, ESkillMoveDirectionType::Left), ESkillMoveResult::PlayerAdvantage);
    CounterTable.Add(MakeTuple(ESkillMoveDirectionType::Middle, ESkillMoveDirectionType::Right), ESkillMoveResult::PlayerAdvantage);
    
    // 玩家下路 vs 敌人各方向
    CounterTable.Add(MakeTuple(ESkillMoveDirectionType::Low, ESkillMoveDirectionType::High), ESkillMoveResult::PlayerWin);
    CounterTable.Add(MakeTuple(ESkillMoveDirectionType::Low, ESkillMoveDirectionType::Middle), ESkillMoveResult::EnemyWin);
    CounterTable.Add(MakeTuple(ESkillMoveDirectionType::Low, ESkillMoveDirectionType::Low), ESkillMoveResult::Draw);
    CounterTable.Add(MakeTuple(ESkillMoveDirectionType::Low, ESkillMoveDirectionType::Left), ESkillMoveResult::EnemyAdvantage);
    CounterTable.Add(MakeTuple(ESkillMoveDirectionType::Low, ESkillMoveDirectionType::Right), ESkillMoveResult::EnemyAdvantage);
    // 玩家左侧 vs 敌人各方向
    CounterTable.Add(MakeTuple(ESkillMoveDirectionType::Left, ESkillMoveDirectionType::High), ESkillMoveResult::EnemyAdvantage);
    CounterTable.Add(MakeTuple(ESkillMoveDirectionType::Left, ESkillMoveDirectionType::Middle), ESkillMoveResult::EnemyAdvantage);
    CounterTable.Add(MakeTuple(ESkillMoveDirectionType::Left, ESkillMoveDirectionType::Low), ESkillMoveResult::PlayerAdvantage);
    CounterTable.Add(MakeTuple(ESkillMoveDirectionType::Left, ESkillMoveDirectionType::Left), ESkillMoveResult::Draw);
    CounterTable.Add(MakeTuple(ESkillMoveDirectionType::Left, ESkillMoveDirectionType::Right), ESkillMoveResult::PlayerWin);
    
    // 玩家右侧 vs 敌人各方向
    CounterTable.Add(MakeTuple(ESkillMoveDirectionType::Right, ESkillMoveDirectionType::High), ESkillMoveResult::EnemyAdvantage);
    CounterTable.Add(MakeTuple(ESkillMoveDirectionType::Right, ESkillMoveDirectionType::Middle), ESkillMoveResult::EnemyAdvantage);
    CounterTable.Add(MakeTuple(ESkillMoveDirectionType::Right, ESkillMoveDirectionType::Low), ESkillMoveResult::PlayerAdvantage);
    CounterTable.Add(MakeTuple(ESkillMoveDirectionType::Right, ESkillMoveDirectionType::Left), ESkillMoveResult::EnemyWin);
    CounterTable.Add(MakeTuple(ESkillMoveDirectionType::Right, ESkillMoveDirectionType::Right), ESkillMoveResult::Draw);
}

void AWX_BattleMag::ProcessMoveResult(ESkillMoveResult Result, ESkillMoveDirectionType PlayerMove, ESkillMoveDirectionType EnemyMove)
{
    // 根据克制结果处理战斗逻辑
    switch (Result)
    {
        case ESkillMoveResult::PlayerWin:
            HandlePlayerWin(PlayerMove, EnemyMove);
            break;
            
        case ESkillMoveResult::EnemyWin:
            HandleEnemyWin(PlayerMove, EnemyMove);
            break;
            
        case ESkillMoveResult::PlayerAdvantage:
            HandlePlayerAdvantage(PlayerMove, EnemyMove);
            break;
            
        case ESkillMoveResult::EnemyAdvantage:
            HandleEnemyAdvantage(PlayerMove, EnemyMove);
            break;
            
        case ESkillMoveResult::Draw:
        default:
            HandleDraw(PlayerMove, EnemyMove);
            break;
    }
    
    // 记录战斗日志
    LogMoveResult(Result, PlayerMove, EnemyMove);
    
    // 触发UI更新事件
    TriggerMoveResultEvent(Result, PlayerMove, EnemyMove);
}

void AWX_BattleMag::HandlePlayerWin(ESkillMoveDirectionType PlayerMove, ESkillMoveDirectionType EnemyMove)
{
    // 玩家获得优势，敌人受到额外伤害
    float DamageMultiplier = 1.5f; // 玩家胜利时伤害增加50%
    
    // 获取当前玩家和敌人单位
    AWX_PlayerCharacterUnit* CurrentPlayer = GetCurrentPlayerUnit();
    AWX_EnemyUnit* CurrentEnemy = GetCurrentEnemyUnit();
    
    if (CurrentPlayer && CurrentEnemy)
    {
        // 计算基础伤害
        float BaseDamage = CalculateBaseDamage(CurrentPlayer, PlayerMove);
        float FinalDamage = BaseDamage * DamageMultiplier;
        
        // 对敌人造成伤害
        CurrentEnemy->TakeDamage(FinalDamage);
        
        // 玩家可能获得额外收益（如内力恢复）
        CurrentPlayer->CharacterAttribute.Stamina += 5.0f;
        
        // 显示特效和音效
        PlayMoveResultEffect(ESkillMoveResult::PlayerWin, CurrentPlayer->GetActorLocation());
        
        UE_LOG(LogTemp, Log, TEXT("玩家招式 %s 克制敌人招式 %s，造成 %.1f 伤害"), 
               *GetMoveDirectionName(PlayerMove), *GetMoveDirectionName(EnemyMove), FinalDamage);
    }
}

void AWX_BattleMag::HandleEnemyWin(ESkillMoveDirectionType PlayerMove, ESkillMoveDirectionType EnemyMove)
{
    // 敌人获得优势，玩家受到额外伤害
    float DamageMultiplier = 1.5f; // 敌人胜利时对玩家伤害增加50%
    
    AWX_PlayerCharacterUnit* CurrentPlayer = GetCurrentPlayerUnit();
    AWX_EnemyUnit* CurrentEnemy = GetCurrentEnemyUnit();
    
    if (CurrentPlayer && CurrentEnemy)
    {
        // 计算基础伤害
        float BaseDamage = CalculateBaseDamage(CurrentEnemy, EnemyMove);
        float FinalDamage = BaseDamage * DamageMultiplier;
        
        // 对玩家造成伤害
        CurrentPlayer->TakeDamage(FinalDamage);
        
        // 敌人可能获得额外收益
        CurrentEnemy->CharacterAttribute.Stamina += 3.0f;
        
        // 显示特效
        PlayMoveResultEffect(ESkillMoveResult::EnemyWin, CurrentEnemy->GetActorLocation());
        
        UE_LOG(LogTemp, Log, TEXT("敌人招式 %s 克制玩家招式 %s，对玩家造成 %.1f 伤害"), 
               *GetMoveDirectionName(EnemyMove), *GetMoveDirectionName(PlayerMove), FinalDamage);
    }
}

void AWX_BattleMag::HandlePlayerAdvantage(ESkillMoveDirectionType PlayerMove, ESkillMoveDirectionType EnemyMove)
{
    // 玩家小幅优势
    float DamageMultiplier = 1.2f; // 伤害增加20%
    
    AWX_PlayerCharacterUnit* CurrentPlayer = GetCurrentPlayerUnit();
    AWX_EnemyUnit* CurrentEnemy = GetCurrentEnemyUnit();
    
    if (CurrentPlayer && CurrentEnemy)
    {
        float BaseDamage = CalculateBaseDamage(CurrentPlayer, PlayerMove);
        float FinalDamage = BaseDamage * DamageMultiplier;
        
        CurrentEnemy->TakeDamage(FinalDamage);
        
        // 小幅收益
        CurrentPlayer->CharacterAttribute.Stamina += 2.0f;
        
        PlayMoveResultEffect(ESkillMoveResult::PlayerAdvantage, CurrentPlayer->GetActorLocation());
        
        UE_LOG(LogTemp, Log, TEXT("玩家招式 %s 略胜敌人招式 %s，造成 %.1f 伤害"), 
               *GetMoveDirectionName(PlayerMove), *GetMoveDirectionName(EnemyMove), FinalDamage);
    }
}

void AWX_BattleMag::HandleEnemyAdvantage(ESkillMoveDirectionType PlayerMove, ESkillMoveDirectionType EnemyMove)
{
    // 敌人小幅优势
    float DamageMultiplier = 1.2f;
    
    AWX_PlayerCharacterUnit* CurrentPlayer = GetCurrentPlayerUnit();
    AWX_EnemyUnit* CurrentEnemy = GetCurrentEnemyUnit();
    
    if (CurrentPlayer && CurrentEnemy)
    {
        float BaseDamage = CalculateBaseDamage(CurrentEnemy, EnemyMove);
        float FinalDamage = BaseDamage * DamageMultiplier;
        
        CurrentPlayer->TakeDamage(FinalDamage);
        CurrentEnemy->CharacterAttribute.Stamina += 1.0f;
        
        PlayMoveResultEffect(ESkillMoveResult::EnemyAdvantage, CurrentEnemy->GetActorLocation());
        
        UE_LOG(LogTemp, Log, TEXT("敌人招式 %s 略胜玩家招式 %s，对玩家造成 %.1f 伤害"), 
               *GetMoveDirectionName(EnemyMove), *GetMoveDirectionName(PlayerMove), FinalDamage);
    }
}

void AWX_BattleMag::HandleDraw(ESkillMoveDirectionType PlayerMove, ESkillMoveDirectionType EnemyMove)
{
    // 平局情况，双方都造成标准伤害
    AWX_PlayerCharacterUnit* CurrentPlayer = GetCurrentPlayerUnit();
    AWX_EnemyUnit* CurrentEnemy = GetCurrentEnemyUnit();
    
    if (CurrentPlayer && CurrentEnemy)
    {
        // 双方都造成基础伤害
        float PlayerDamage = CalculateBaseDamage(CurrentPlayer, PlayerMove);
        float EnemyDamage = CalculateBaseDamage(CurrentEnemy, EnemyMove);
        
        CurrentEnemy->TakeDamage(PlayerDamage);
        CurrentPlayer->TakeDamage(EnemyDamage);
        
        PlayMoveResultEffect(ESkillMoveResult::Draw, 
                           (CurrentPlayer->GetActorLocation() + CurrentEnemy->GetActorLocation()) * 0.5f);
        
        UE_LOG(LogTemp, Log, TEXT("玩家招式 %s 与敌人招式 %s 势均力敌，双方各受 %.1f 和 %.1f 伤害"), 
               *GetMoveDirectionName(PlayerMove), *GetMoveDirectionName(EnemyMove), 
               EnemyDamage, PlayerDamage);
    }
}

AWX_PlayerCharacterUnit* AWX_BattleMag::GetCurrentPlayerUnit()
{
    if (AllBattleUnits.IsValidIndex(CurrentCharacterIndex))
    {
        return Cast<AWX_PlayerCharacterUnit>(AllBattleUnits[CurrentCharacterIndex]);
    }
    
    // 如果当前不是玩家回合，返回第一个玩家单位
    if (BattlePlayerUnits.Num() > 0)
    {
        return Cast<AWX_PlayerCharacterUnit>(BattlePlayerUnits[0]);
    }
    
    return nullptr;
}

AWX_EnemyUnit* AWX_BattleMag::GetCurrentEnemyUnit()
{
    // 获取当前对战的敌人，当玩家选择攻击谁的时候
    if (BattleEnemyUnits.Num() > 0)
    {
        return Cast<AWX_EnemyUnit>(BattleEnemyUnits[0]);
    }
    return nullptr;
}

float AWX_BattleMag::CalculateBaseDamage(AWX_CharacterUnitBase* Attacker, ESkillMoveDirectionType MoveType)
{
    if (!Attacker) return 0.0f;
    
    float BaseDamage = Attacker->CharacterAttribute.Strength; // 基于力量属性
    
    // 根据招式类型调整伤害
    switch (MoveType)
    {
        case ESkillMoveDirectionType::High:
            BaseDamage *= 1.1f; // 上路攻击伤害稍高
            break;
        case ESkillMoveDirectionType::Middle:
            BaseDamage *= 1.0f; // 中路标准伤害
            break;
        case ESkillMoveDirectionType::Low:
            BaseDamage *= 0.9f; // 下路攻击伤害稍低但更稳定
            break;
        case ESkillMoveDirectionType::Left:
        case ESkillMoveDirectionType::Right:
            BaseDamage *= 0.95f; // 侧向攻击伤害略低
            break;
    }
    
    return BaseDamage;
}

FString AWX_BattleMag::GetMoveDirectionName(ESkillMoveDirectionType MoveType)
{
    switch (MoveType)
    {
        case ESkillMoveDirectionType::High: return TEXT("上路");
        case ESkillMoveDirectionType::Middle: return TEXT("中路");
        case ESkillMoveDirectionType::Low: return TEXT("下路");
        case ESkillMoveDirectionType::Left: return TEXT("左侧");
        case ESkillMoveDirectionType::Right: return TEXT("右侧");
        default: return TEXT("未知");
    }
}

void AWX_BattleMag::PlayMoveResultEffect(ESkillMoveResult Result, const FVector& Location)
{
    // 根据结果播放不同的特效
    switch (Result)
    {
        case ESkillMoveResult::PlayerWin:
            // 播放玩家胜利特效（如金光闪闪）
            UE_LOG(LogTemp, Warning, TEXT("播放玩家大胜特效"));
            break;
        case ESkillMoveResult::EnemyWin:
            // 播放敌人胜利特效（如红光）
            UE_LOG(LogTemp, Warning, TEXT("播放敌人大胜特效"));
            break;
        case ESkillMoveResult::PlayerAdvantage:
            // 播放玩家优势特效（如蓝光）
            UE_LOG(LogTemp, Warning, TEXT("播放玩家小胜特效"));
            break;
        case ESkillMoveResult::EnemyAdvantage:
            // 播放敌人优势特效（如紫光）
            UE_LOG(LogTemp, Warning, TEXT("播放敌人小胜特效"));
            break;
        case ESkillMoveResult::Draw:
            // 播放平局特效（如白光碰撞）
            UE_LOG(LogTemp, Warning, TEXT("播放平局特效"));
            break;
    }
}

void AWX_BattleMag::LogMoveResult(ESkillMoveResult Result, ESkillMoveDirectionType PlayerMove, ESkillMoveDirectionType EnemyMove)
{
    FString ResultText;
    switch (Result)
    {
        case ESkillMoveResult::PlayerWin: ResultText = TEXT("玩家大胜"); break;
        case ESkillMoveResult::EnemyWin: ResultText = TEXT("敌人大胜"); break;
        case ESkillMoveResult::PlayerAdvantage: ResultText = TEXT("玩家小胜"); break;
        case ESkillMoveResult::EnemyAdvantage: ResultText = TEXT("敌人小胜"); break;
        case ESkillMoveResult::Draw: ResultText = TEXT("势均力敌"); break;
    }	
}

void AWX_BattleMag::TriggerMoveResultEvent(ESkillMoveResult Result, ESkillMoveDirectionType PlayerMove, ESkillMoveDirectionType EnemyMove)
{
    if (EventSystem)
    {
        // 创建招式结果事件
        FGameplayTag MoveResultTag = FGameplayTag::RequestGameplayTag(FName("Event.Battle.MoveResult"));
        UWX_EventPayload* Payload = EventSystem->CreateEventPayload(EEventType::Combat, MoveResultTag);
        
        // 可以在Payload中添加更多信息
        Payload->EventSource = this;
        
        EventSystem->TriggerEvent(Payload);
    }
}

void AWX_BattleMag::MeleeAttack(AWX_CharacterUnitBase* Attacker, AWX_CharacterUnitBase* Target)
{
    if (!Attacker || !Target) return;
    
    // 基础近战攻击逻辑
    float Damage = Attacker->CharacterAttribute.Strength;
    Target->TakeDamage(Damage);
    
    UE_LOG(LogTemp, Log, TEXT("%s 对 %s 进行近战攻击，造成 %.1f 伤害"), 
           *Attacker->GetName(), *Target->GetName(), Damage);
}

void AWX_BattleMag::RangedAttack(AWX_CharacterUnitBase* Attacker, AWX_CharacterUnitBase* Target)
{
    if (!Attacker || !Target) return;
    
    // 基础远程攻击逻辑
    float Damage = Attacker->CharacterAttribute.Strength * 0.8f; // 远程攻击伤害稍低
    Target->TakeDamage(Damage);
    
    UE_LOG(LogTemp, Log, TEXT("%s 对 %s 进行远程攻击，造成 %.1f 伤害"), 
           *Attacker->GetName(), *Target->GetName(), Damage);
}

void AWX_BattleMag::EndBattleTurn()
{
	EventSystem->CreateEventAndPayload("Event.Battle.EndBattle");
}

void AWX_BattleMag::CheckOutBattleProximity(AWX_PlayerCharacterUnit* CurrentUnit)
{
	if (!CurrentUnit)
	{
		return;
	}
	if (BattleEnemyUnits.Num()==0)
	{
		//切换为探索模式
		AllBattleUnits.Empty();
		BattlePlayerUnits.Empty();
		return;
	}

	bool bIsPlayer = BattlePlayerUnits.Contains(CurrentUnit);
	bool bIsEnemy = BattleEnemyUnits.Contains(CurrentUnit);
	if (!bIsPlayer && !bIsEnemy) return;

	const TArray<AWX_CharacterUnitBase*>& OpponentGroup = bIsPlayer ? BattleEnemyUnits : BattlePlayerUnits;

	bool bInBattleRange = false;

	for (AWX_CharacterUnitBase* Opponent : OpponentGroup)
	{
		if (!Opponent) continue;

		float Distance = FVector::Dist(CurrentUnit->GetActorLocation(), Opponent->GetActorLocation());
		if (Distance <= BattleDistance)
		{
			bInBattleRange = true;
			break;
		}
	}

	if (!bInBattleRange)
	{
		CurrentUnit->isInBattle=false;
		if (bIsPlayer)
		{
			BattlePlayerUnits.Remove(CurrentUnit);
			
		}
		else if (bIsEnemy)
		{
			BattleEnemyUnits.Remove(CurrentUnit);
		}

		AllBattleUnits.Remove(CurrentUnit);
	}
}

void AWX_BattleMag::InsertBattleUnitSorted(AWX_CharacterUnitBase* CurrentUnit)
{
	if (!CurrentUnit) return;

	// 假设你已有接口或方法可以获取 initiative 值
	float NewInit = CurrentUnit->CharacterAttribute.Dexterity;

	int32 InsertIndex = 0;
	for (; InsertIndex < AllBattleUnits.Num(); ++InsertIndex)
	{
		AWX_PlayerCharacterUnit* ExistingUnit =Cast<AWX_PlayerCharacterUnit>(AllBattleUnits[InsertIndex]) ;
		if (!ExistingUnit) continue;

		float ExistingInit = ExistingUnit->CharacterAttribute.Dexterity;

		// 假设你希望 Initiative 值高的排在前面
		if (NewInit > ExistingInit)
		{
			break;
		}
	}

	AllBattleUnits.Insert(CurrentUnit, InsertIndex);
}


