// Fill out your copyright notice in the Description page of Project Settings.


#include "Component/WX_BattleMag.h"
#include "Character/WX_CharacterController.h"
#include "Character/WX_CharacterUnitBase.h"
#include "Character/WX_PlayerCharacterUnit.h"
#include "Enemy/WX_EnemyAIController.h"
#include "Enemy/WX_EnemyUnit.h"
#include "Event/WX_EventSystem.h"
#include "Kismet/GameplayStatics.h"
#include "Kismet/KismetSystemLibrary.h"


// Sets default values for this component's properties
AWX_BattleMag::AWX_BattleMag()
{
}


// Called when the game starts
void AWX_BattleMag::BeginPlay()
{
	Super::BeginPlay();
	PawnController=Cast<AWX_CharacterController>(UGameplayStatics::GetPlayerController(GetWorld(), 0));
	EventSystem = GetGameInstance()->GetSubsystem<UWX_EventSystem>();
	if (EventSystem)
	{
		EventSystem->RegisterEvent<AWX_BattleMag>(FName("Event.Battle.EnterBattle"),this,&AWX_BattleMag::EnterBattleTurn);
		EventSystem->RegisterEvent<AWX_BattleMag>(FName("Event.Battle.NextCharacter"),this,&AWX_BattleMag::NextCharacterReady);
		EventSystem->RegisterEvent<AWX_BattleMag>(FName("Event.Battle.Dead"),this,&AWX_BattleMag::RemoveCharacterFromListIfDead);
	}
}

void AWX_BattleMag::RemoveCharacterFromListIfDead(UWX_EventPayload* Uwx_EventPayload)
{
	AWX_CharacterUnitBase* DeadCharacter=Cast<AWX_CharacterUnitBase>(Uwx_EventPayload->EventSource);
	DeadCharacter->CurrentBattleIndex=CurrentCharacterIndex;
	AllBattleUnits.Remove(DeadCharacter);
}

//通过AI感知触发，对话触发
void AWX_BattleMag::EnterBattleTurn(UWX_EventPayload* Payload)
{
	//读取配置表
	FGameplayTag MyEventID = FGameplayTag::RequestGameplayTag(FName("Event.Data.UpDataTable"));
	TSoftObjectPtr<UWX_EventPayload> TempPayload =EventSystem->CreateEventPayload(EEventType::Combat, MyEventID);
	TempPayload->EventSource = this;
	EventSystem->TriggerEvent(TempPayload.Get());
	//生成一个overspherelap获取所有Units，并分类
		TArray<AActor*> OverlappedActors;
		if (PawnController)
		{
			TArray<TEnumAsByte<EObjectTypeQuery>> ObjectTypes;
			ObjectTypes.Add(UEngineTypes::ConvertToObjectType(ECollisionChannel::ECC_Pawn));
		
			UKismetSystemLibrary::SphereOverlapActors(
				this,
				PawnController->GetSelectedUnits()[0]->GetActorLocation(),
				BattleEnterRadius,
				ObjectTypes,
				ACharacter::StaticClass(),
				TArray<AActor*>(),
				OverlappedActors
			);

			if (IsEnterDebugSphere)
			{
				DrawDebugSphere(
					GetWorld(),
				PawnController->GetSelectedUnits()[0]->GetActorLocation(),
					BattleEnterRadius,
					32,                       // 球体分段数（越大越圆）
					FColor::Red,
					false,                   // 不永久显示
					 5.f,                     // 显示时间，秒
			0,
					1.0f                     // 线宽
				);
			}
		
		}

		//对OverlappedActors进行分类
		for (AActor* units:OverlappedActors)
		{
			AWX_CharacterUnitBase* Unit=Cast<AWX_CharacterUnitBase>(units);
			if (Unit)
			{
				Unit->CharacterState= ECharacterState::Combat;
				AllBattleUnits.Add(Unit);
				//简单分类，后续可以根据阵营等复杂条件分类
				if (Unit->CharacterType==ECharacterType::Player&&!BattlePlayerUnits.Contains(Unit))
				{
					BattlePlayerUnits.Add(Unit);
				}
				if (Unit->CharacterType==ECharacterType::Enemy&&!BattleEnemyUnits.Contains(Unit))
				{
					AWX_EnemyUnit* EnemyUnit=Cast<AWX_EnemyUnit>(Unit);
					EnemyUnit->CombatState();
					BattleEnemyUnits.Add(Unit);
				}
			}
		}

		GEngine->AddOnScreenDebugMessage(-1, 5.f, FColor::Green, FString::Printf(TEXT("Enter Battle Turn: %d Player Units, %d Enemy Units"), BattlePlayerUnits.Num(), BattleEnemyUnits.Num()));
		
		//根据速度对AllbattleUnits排序，快的在前面
		AllBattleUnits.Sort([](const AWX_CharacterUnitBase& A, const AWX_CharacterUnitBase& B)
		{
			//身法
			return A.CharacterAttribute.Dexterity > B.CharacterAttribute.Dexterity;
		});

		//开始对allbattleunits进行逐一递增，并判断是玩家回合还是AI回合，执行对应函数
	if (AllBattleUnits.Num()>0)
	{
		if (AllBattleUnits[CurrentCharacterIndex]->CharacterType==ECharacterType::Player)
		{
			AWX_PlayerCharacterUnit* CurrentUnit=Cast<AWX_PlayerCharacterUnit>(AllBattleUnits[CurrentCharacterIndex]);
			PawnController->TargetUnit=CurrentUnit;
			PawnController->PreviousTargetUnit=CurrentUnit;
			PlayerTurn(CurrentUnit);
		}
		else if (AllBattleUnits[CurrentCharacterIndex]->CharacterType==ECharacterType::Enemy)
		{
			AWX_EnemyUnit* CurrentUnit=Cast<AWX_EnemyUnit>(AllBattleUnits[CurrentCharacterIndex]);
			AITurn(CurrentUnit);
		}
	}
	
	Payload->EventId=FGameplayTag::RequestGameplayTag(FName("Event.Battle.BattleUI"));
	Payload->EventSource=this;
	Payload->EventType=EEventType::Combat;
	EventSystem->TriggerEvent(Payload);
		
}


void AWX_BattleMag::PlayerTurn(AWX_PlayerCharacterUnit* CurrentUnit)
{
	if (IBP_CombatBehaviorInterface* CombatUnit=Cast<IBP_CombatBehaviorInterface>(CurrentUnit))
	{
		CombatUnit->Execute_StartTurn(CurrentUnit);
	}
}

void AWX_BattleMag::AITurn(AWX_EnemyUnit* CurrentUnit)
{
	// 使用接口统一处理
	if (IBP_CombatBehaviorInterface* CombatUnit = Cast<IBP_CombatBehaviorInterface>(CurrentUnit))
	{
		if (CurrentUnit)
		{
			CombatUnit->Execute_StartTurn(CurrentUnit);
		}
		// AI会自动执行行动，然后调用EndTurn
		// 这可以通过事件系统或回调来处理
	}
}

void AWX_BattleMag::NextCharacterReady(UWX_EventPayload* Payload)
{
	PawnController->PreviousTargetUnit->IsBeenSelected=false;
	CurrentCharacterIndex++;
	if (CurrentCharacterIndex>=AllBattleUnits.Num())
	{
		CurrentCharacterIndex=0;
	}
	if (AllBattleUnits[CurrentCharacterIndex]->CharacterType==ECharacterType::Player)
	{
		//恢复行动力
		if (AllBattleUnits[CurrentCharacterIndex]->CurrentActionPoints==0)
		{
			AllBattleUnits[CurrentCharacterIndex]->CurrentActionPoints=AllBattleUnits[CurrentCharacterIndex]->CharacterAttribute.ActionPoints;
		}
		AWX_PlayerCharacterUnit* CurrentUnit=Cast<AWX_PlayerCharacterUnit>(AllBattleUnits[CurrentCharacterIndex]);
		PawnController->TargetUnit=CurrentUnit;
		PawnController->PreviousTargetUnit=CurrentUnit;
		PlayerTurn(CurrentUnit);
	}
	else if (AllBattleUnits[CurrentCharacterIndex]->CharacterType==ECharacterType::Enemy)
	{
		AWX_EnemyUnit* CurrentUnit=Cast<AWX_EnemyUnit>(AllBattleUnits[CurrentCharacterIndex]);
		AITurn(CurrentUnit);
		NextCharacterReady(Payload);
	}
}

void AWX_BattleMag::CheckBattleSkillMove(ESkillMoveDirectionType PlayerSkillMove,
	ESkillMoveDirectionType EnemySkillMove)
{
	//招式克制逻辑 shan
}

void AWX_BattleMag::EndBattleTurn()
{
	EventSystem->CreateEventAndPayload("Event.Battle.EndBattle");
}

void AWX_BattleMag::CheckOutBattleProximity(AWX_PlayerCharacterUnit* CurrentUnit)
{
	if (!CurrentUnit)
	{
		return;
	}
	if (BattleEnemyUnits.Num()==0)
	{
		//切换为探索模式
		AllBattleUnits.Empty();
		BattlePlayerUnits.Empty();
		return;
	}

	bool bIsPlayer = BattlePlayerUnits.Contains(CurrentUnit);
	bool bIsEnemy = BattleEnemyUnits.Contains(CurrentUnit);
	if (!bIsPlayer && !bIsEnemy) return;

	const TArray<AWX_CharacterUnitBase*>& OpponentGroup = bIsPlayer ? BattleEnemyUnits : BattlePlayerUnits;

	bool bInBattleRange = false;

	for (AWX_CharacterUnitBase* Opponent : OpponentGroup)
	{
		if (!Opponent) continue;

		float Distance = FVector::Dist(CurrentUnit->GetActorLocation(), Opponent->GetActorLocation());
		if (Distance <= BattleDistance)
		{
			bInBattleRange = true;
			break;
		}
	}

	if (!bInBattleRange)
	{
		CurrentUnit->isInBattle=false;
		if (bIsPlayer)
		{
			BattlePlayerUnits.Remove(CurrentUnit);
			
		}
		else if (bIsEnemy)
		{
			BattleEnemyUnits.Remove(CurrentUnit);
		}

		AllBattleUnits.Remove(CurrentUnit);
	}
}

void AWX_BattleMag::InsertBattleUnitSorted(AWX_CharacterUnitBase* CurrentUnit)
{
	if (!CurrentUnit) return;

	// 假设你已有接口或方法可以获取 initiative 值
	float NewInit = CurrentUnit->CharacterAttribute.Dexterity;

	int32 InsertIndex = 0;
	for (; InsertIndex < AllBattleUnits.Num(); ++InsertIndex)
	{
		AWX_PlayerCharacterUnit* ExistingUnit =Cast<AWX_PlayerCharacterUnit>(AllBattleUnits[InsertIndex]) ;
		if (!ExistingUnit) continue;

		float ExistingInit = ExistingUnit->CharacterAttribute.Dexterity;

		// 假设你希望 Initiative 值高的排在前面
		if (NewInit > ExistingInit)
		{
			break;
		}
	}

	AllBattleUnits.Insert(CurrentUnit, InsertIndex);
}