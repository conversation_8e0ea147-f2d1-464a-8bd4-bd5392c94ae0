// Fill out your copyright notice in the Description page of Project Settings.


#include "Component/WX_FollowSystemComponent.h"

#include "AIController.h"
#include "Character/WX_PlayerCharacterUnit.h"
#include "Components/ArrowComponent.h"
#include "GameFramework/Character.h"
#include "Kismet/GameplayStatics.h"



// Sets default values for this component's properties
UWX_FollowSystemComponent::UWX_FollowSystemComponent()
{
	PrimaryComponentTick.bCanEverTick = true;
}


// Called when the game starts
void UWX_FollowSystemComponent::BeginPlay()
{
	Super::BeginPlay();
	GetTeamMembers();
}

void UWX_FollowSystemComponent::TickComponent(float DeltaTime, enum ELevelTick TickType,
	FActorComponentTickFunction* ThisTickFunction)
{
	Super::TickComponent(DeltaTime, TickType, ThisTickFunction);
	UpdateFollowPositions();
}


void UWX_FollowSystemComponent::SetTeamLeader(AWX_PlayerCharacterUnit* NewLeader)
{
	//队伍模式下才执行
	
	if (!NewLeader||!CharacterTeamUnits.Contains(NewLeader))
	{
		UE_LOG(LogTemp, Warning, TEXT("此选择的队长不在团队中或无效"));
		return;
	}

	CurrentLeader=NewLeader;

	//清空跟随者列表
	Followers.Empty();

	//构建新的跟随者列表
	for (AActor* Unit:CharacterTeamUnits)
	{
		if (Unit&&Unit!= CurrentLeader && Unit->IsA(ACharacter::StaticClass()))
		{
			AWX_PlayerCharacterUnit * PlayerUnit = Cast<AWX_PlayerCharacterUnit>(Unit);
			Followers.Add(PlayerUnit);
		}
	}

	//停止队长AI移动
	if (AAIController* LeaderAI  = GetAIController(CurrentLeader))
	{
		LeaderAI ->StopMovement();
	}

	GEngine->AddOnScreenDebugMessage(-1, 5.f, FColor::Green, FString::Printf(TEXT("新队长设置为: %s"), *NewLeader->GetName()));
}

void UWX_FollowSystemComponent::UpdateFollowPositions()
{
	if (!CurrentLeader || Followers.Num() == 0)
	{
		UE_LOG(LogTemp, Warning, TEXT("没有有效的队长或跟随者"));
		return;
	}

	// 根据跟随者数量分配位置
	for (int32 i = 0; i < Followers.Num(); i++)
	{
		if (!Followers[i])
			continue;
            
		bool bIsLeftSide;
        
		// 分配左右位置的逻辑
		if (Followers.Num() == 1)
		{
			// 只有一个跟随者，放在右侧
			bIsLeftSide = false;
		}
		else if (Followers.Num() == 2)
		{
			// 两个跟随者，一个左一个右
			bIsLeftSide = (i == 0);
		}
		else
		{
			// 多个跟随者，交替分配左右
			bIsLeftSide = (i % 2 == 0);
		}
        
		FVector TargetPosition = CalculateFollowPosition(CurrentLeader, bIsLeftSide);
        
		// 检查距离，决定是否需要移动
		// float DistanceToTarget = FVector::Dist(Followers[i]->GetActorLocation(), TargetPosition);
  //       
		// if (DistanceToTarget > StopDistance)
		// {
			MoveFollowerToPosition(Followers[i], TargetPosition);
		// }
	}
}

FVector UWX_FollowSystemComponent::CalculateFollowPosition(AWX_PlayerCharacterUnit* Leader, bool bIsLeftSide)
{
	if (!Leader)
	{
		return FVector::ZeroVector;
	}
    
	// 获取队长的方向向量
	// FVector LeaderLocation = Leader->GetActorLocation();
	// FVector LeaderForward = Leader->GetActorForwardVector();
	// FVector LeaderRight = Leader->GetActorRightVector();
    
	// 计算后方位置
	// FVector BackwardPosition = LeaderLocation - (LeaderForward * FollowDistance);
	FVector BackwardPosition;
	if (bIsLeftSide)
	{
		BackwardPosition = Leader->LeftFollowPosition->K2_GetComponentLocation();
	}
	else
	{
		BackwardPosition = Leader->RightFollowPosition->K2_GetComponentLocation();
	}
    
	// 计算左右偏移
	// float SideMultiplier = bIsLeftSide ? -1.0f : 1.0f;
	// FVector SidePosition = BackwardPosition + (LeaderRight * SideOffset * SideMultiplier);
	// FVector SidePosition = BackwardPosition * SideMultiplier;
    
	return BackwardPosition;
}

void UWX_FollowSystemComponent::GetTeamMembers()
{
	UGameplayStatics::GetAllActorsOfClass(GetWorld(), AWX_PlayerCharacterUnit::StaticClass(), CharacterTeamUnits);
}

void UWX_FollowSystemComponent::MoveFollowerToPosition(AWX_PlayerCharacterUnit* Follower, FVector TargetPosition)
{
	if (!Follower)
		return;
        
	AAIController* AIController = GetAIController(Follower);
	if (AIController)
	{
		// 使用AI移动到目标位置
		AIController->MoveToLocation(TargetPosition, 50.f);
	}
}

AAIController* UWX_FollowSystemComponent::GetAIController(AWX_PlayerCharacterUnit* Character)
{
	if (!Character)
		return nullptr;
        
	if (APawn* Pawn = Cast<APawn>(Character))
	{
		return Cast<AAIController>(Pawn->GetController());
	}
    
	return nullptr;
}

