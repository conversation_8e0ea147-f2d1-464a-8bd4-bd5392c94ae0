#pragma once
#include "CoreMinimal.h"
#include "WX_CharacterSkillStruct.generated.h"



//攻击距离
UENUM()
enum class EAttackRangeType:uint8
{
	Melee UMETA(DisplayName = "近战"),
	Ranged UMETA(DisplayName = "远程"),
};

//攻击目标类型
UENUM()
enum class EAttackTargetType : uint8
{
	Single UMETA(DisplayName = "单体"),
	AOE_CIRCLE UMETA(DisplayName = "范围-圆形"),
	AOE_LINE UMETA(DisplayName = "范围-直线"),
	AOE_CONE UMETA(DisplayName = "范围-扇形"),
	SELF UMETA(DisplayName = "自身/光环"),
};

//技能属性系别
UENUM()
enum class ESkillAttributeType : uint8
{
	NEUTRAL	UMETA(DisplayName = "无属性"),
	FIRE UMETA(DisplayName = "火系"),
	WATER UMETA(DisplayName = "水/冰系"),
	WOOD UMETA(DisplayName = "木/毒"),
	METAL UMETA(DisplayName = "金/锋锐"),
	EARTH UMETA(DisplayName = "土"),
	INNER UMETA(DisplayName = "内劲"),
};

//技能类型
UENUM()
enum class ESkillCategory : uint8
{
	BASIC_ATTACK UMETA(DisplayName = "基础攻击"),
	INTERNAL UMETA(DisplayName = "内功/状态"),
	LIGHTNESS_SKILL UMETA(DisplayName = "身法"),
	STRIKE UMETA(DisplayName = "拳脚"),
	SWORD UMETA(DisplayName = "剑法"),
	SABER UMETA(DisplayName = "刀法"),
	STAFF UMETA(DisplayName = "棍法"),
	HIDDEN_WEAPON UMETA(DisplayName = "暗器"),
};

//技能作用效果
UENUM()
enum class ESkillEffectType : uint8
{
	DAMAGE UMETA(DisplayName = "伤害"),
	HEAL UMETA(DisplayName = "治疗"),
	BUFF UMETA(DisplayName = "增益"),
	DEBUFF UMETA(DisplayName = "减益/削弱"),
	CONTROL UMETA(DisplayName = "控制/封穴/击退"),
};

//技能施放方式
UENUM()
enum class ESkillCastType : uint8
{
	INSTANT UMETA(DisplayName = "瞬发"),
	CHARGE UMETA(DisplayName = "蓄力"),
	CHARGED UMETA(DisplayName = "蓄力后释放"),
	PASSIVE UMETA(DisplayName = "被动"),
};

//技能冷却类型
UENUM()
enum class ECooldownType : uint8
{
	NO_COOLDOWN UMETA(DisplayName = "无冷却"),
	COOLDOWN UMETA(DisplayName = "单独冷却"),
	RESOURCE_BASED UMETA(DisplayName = "基于内力/体力消耗"),
	SHARED_COOLDOWN UMETA(DisplayName = "共享冷却"),
};


USTRUCT()
struct WUXIA_API FCharacterSkill
{
	GENERATED_BODY()

	// 技能ID
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Skill")
	int32 SkillId;

	// 技能名称
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Skill")
	FString SkillName;

	//技能伤害
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Skill")
	float Damage;

	//技能额外效果骰子概率
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Skill")
	float ExtraEffectProbability;

	//技能攻击距离
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Skill")
	EAttackRangeType AttackRangeType;

	//技能攻击目标类型
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Skill")
	EAttackTargetType AttackTargetType;

	// 技能描述
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Skill")
	FString SkillDescription;

	//技能类型
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Skill")
	ESkillCategory SkillCategory;

	//技能冷却
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Skill")
	ECooldownType CooldownType;

	//技能作用效果
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Skill")
	ESkillEffectType SkillEffectType;

	//技能属性系别
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Skill")
	ESkillAttributeType SkillAttributeType;
};