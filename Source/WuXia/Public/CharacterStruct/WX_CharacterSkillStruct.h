#pragma once
#include "CoreMinimal.h"
#include "WX_CharacterSkillStruct.generated.h"



//攻击距离
UENUM(BlueprintType)
enum class EAttackRangeType:uint8
{
	Melee UMETA(DisplayName = "近战"),
	Ranged UMETA(DisplayName = "远程"),
};

//攻击目标类型
UENUM(BlueprintType)
enum class EAttackTargetType : uint8
{
	Single UMETA(DisplayName = "单体"),
	AOE_CIRCLE UMETA(DisplayName = "范围-圆形"),
	AOE_LINE UMETA(DisplayName = "范围-直线"),
	AOE_CONE UMETA(DisplayName = "范围-扇形"),
	SELF UMETA(DisplayName = "自身/光环"),
};

//技能属性系别
UENUM(BlueprintType)
enum class ESkillAttributeType : uint8
{
	NEUTRAL	UMETA(DisplayName = "无属性"),
	FIRE UMETA(DisplayName = "火系"),
	WATER UMETA(DisplayName = "水/冰系"),
	WOOD UMETA(DisplayName = "木/毒"),
	METAL UMETA(DisplayName = "金/锋锐"),
	EARTH UMETA(DisplayName = "土"),
	INNER UMETA(DisplayName = "内劲"),
};

//技能类型
UENUM(BlueprintType)
enum class ESkillCategory : uint8
{
	BASIC_ATTACK UMETA(DisplayName = "基础攻击"),
	INTERNAL UMETA(DisplayName = "内功/状态"),
	LIGHTNESS_SKILL UMETA(DisplayName = "身法"),
	STRIKE UMETA(DisplayName = "拳脚"),
	SWORD UMETA(DisplayName = "剑法"),
	SABER UMETA(DisplayName = "刀法"),
	STAFF UMETA(DisplayName = "棍法"),
	HIDDEN_WEAPON UMETA(DisplayName = "暗器"),
};

//技能作用效果
UENUM(BlueprintType)
enum class ESkillEffectType : uint8
{
	DAMAGE UMETA(DisplayName = "伤害"),
	HEAL UMETA(DisplayName = "治疗"),
	BUFF UMETA(DisplayName = "增益"),
	DEBUFF UMETA(DisplayName = "减益/削弱"),
	CONTROL UMETA(DisplayName = "控制/封穴/击退"),
	PASSIVE UMETA(DisplayName = "被动"),
};

//技能施放方式
UENUM(BlueprintType)
enum class ESkillCastType : uint8
{
	INSTANT UMETA(DisplayName = "瞬发"),
	CHARGE UMETA(DisplayName = "蓄力"),
	CHARGED UMETA(DisplayName = "蓄力后释放"),
};

//技能冷却类型
UENUM(BlueprintType)
enum class ECooldownType : uint8
{
	NO_COOLDOWN UMETA(DisplayName = "无冷却"),
	COOLDOWN UMETA(DisplayName = "单独冷却"),
	RESOURCE_BASED UMETA(DisplayName = "基于内力/体力消耗"),
	SHARED_COOLDOWN UMETA(DisplayName = "共享冷却"),
};

//技能效果持续类型枚举
UENUM(BlueprintType)
enum class ESkillEffectDurationType : uint8
{
	Instant UMETA(DisplayName = "瞬时效果"),
	Duration UMETA(DisplayName = "持续时间"),
	Permanent UMETA(DisplayName = "永久效果"),
	UntilRemoved UMETA(DisplayName = "直到被移除"),
	Conditional UMETA(DisplayName = "条件性持续"),
};

//技能效果堆叠类型枚举
UENUM(BlueprintType)
enum class ESkillEffectStackType : uint8
{
	None UMETA(DisplayName = "不可堆叠"),
	Replace UMETA(DisplayName = "替换旧效果"),
	Stack UMETA(DisplayName = "可以堆叠"),
	Refresh UMETA(DisplayName = "刷新持续时间"),
	Extend UMETA(DisplayName = "延长持续时间"),
};

//招式判定区域
UENUM(BlueprintType)
enum class ESkillMoveDirectionType : uint8
{
	High UMETA(DisplayName = "上"),
	Middle UMETA(DisplayName = "中"),
	Low UMETA(DisplayName = "下"),
	Left UMETA(DisplayName = "左"),
	Right UMETA(DisplayName = "右"),
};

UENUM(BlueprintType)
enum class ESkillMoveResult : uint8
{
	Draw = 0,           // 平局
	PlayerWin,          // 玩家胜利
	EnemyWin,           // 敌人胜利
	PlayerAdvantage,    // 玩家优势（小胜）
	EnemyAdvantage      // 敌人优势（小胜）
};


//技能招式
USTRUCT(BlueprintType)
struct WUXIA_API FCharacterSkillMove:public FTableRowBase
{
	GENERATED_BODY()

	//招式属于哪套
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Skill")
	int32 MoveParentID;

	//招式ID
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Skill")
	int32 MoveId;

	//招式名称
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Skill")
	FString MoveName;

	
	//是否为衍生招式
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Skill")
	bool bIsDerivedMove;

	//衍生需要那些指令
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Skill",meta=(EditCondition="bIsDerivedMove"))
	TArray<ESkillMoveDirectionType> RequiredMoves;

	//招式伤害
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Skill")
	float Damage;

	//招式消耗
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Skill")
	float Cost;

	//招式描述
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Skill")
	FString MoveDescription;

	//招式判定区域
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Skill",meta=(EditCondition ="!bIsDerivedMove"))
	ESkillMoveDirectionType MoveDirection;

};


//技能
USTRUCT(BlueprintType)
struct WUXIA_API FCharacterSkill:public FTableRowBase
{
	GENERATED_BODY()

	// 技能ID
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Skill")
	int32 SkillId;

	// 技能名称
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Skill")
	FText SkillName;

	//技能招式
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Skill")
	TArray<FCharacterSkillMove> SkillMove;

	//技能伤害
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Skill")
	float Damage;

	//技能额外效果骰子概率
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Skill")
	float ExtraEffectProbability;

	//技能攻击距离
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Skill")
	EAttackRangeType AttackRangeType;

	//技能攻击目标类型
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Skill")
	EAttackTargetType AttackTargetType;

	// 技能描述
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Skill")
	FString SkillDescription;

	//技能类型
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Skill")
	ESkillCategory SkillCategory;

	//技能冷却
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Skill")
	ECooldownType CooldownType;

	//技能作用效果
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Skill")
	ESkillEffectType SkillEffectType;

	//技能属性系别
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Skill")
	ESkillAttributeType SkillAttributeType;

	//技能招式组合
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Skill")
	TArray<ESkillMoveDirectionType> SkillMoveCombination;
};

