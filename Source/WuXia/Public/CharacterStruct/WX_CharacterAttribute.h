#pragma once

#include "CoreMinimal.h"
#include "WX_CharacterAttribute.generated.h"


USTRUCT(BlueprintType)
struct WUXIA_API FWX_CharacterAttribute
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attributes")
	TSubclassOf<UTexture2D> CharacterIcon;
	//生命
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attributes")
	float Health;

	//内力
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attributes")
	float Stamina;

	//经验
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attributes")
	int32 Experience;

	//行动力
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attributes")
	int32 ActionPoints;

	//悟性
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attributes")
	int32 Insight;

	//道德
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attributes")
	int32 Morality;

	//名声
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attributes")
	int32 Fame;

	//臂力
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attributes")
	float Strength;

	//内劲
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attributes")
	float Agility;

	//体魄
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attributes")
	float Intelligence;

	//身法
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attributes")
	float Dexterity;

	//灵巧
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attributes")
	float Charisma;

	//意志
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attributes")
	float Willpower;

	FWX_CharacterAttribute()
		: Health(100.0f)
		, Stamina(100.0f)
		, ActionPoints(10.0f)
		, Strength(10.0f)
		, Agility(10.0f)
		, Intelligence(10.0f)
		, Dexterity(10.0f)
		, Charisma(10.0f)
		, Willpower(10.0f)
	{
	}
};

//角色类型
UENUM()
enum class ECharacterType:uint8
{
	Enemy,
	Player,
	NPC,
};

//角色状态
UENUM(BlueprintType)
enum class ECharacterState : uint8
{
	Exploration UMETA(DisplayName = "Exploration"),
	Combat UMETA(DisplayName = "Combat"),
};

//角色队伍类型
UENUM()
enum class EPlayerChacterTeamType: uint8
{
	Team UMETA(DisplayName = "Team"),
	//分离
	Separate UMETA(DisplayName = "Separate"),
};



//行动力消耗类型
UENUM()
enum class EActionCostType: uint8
{
	NO_COST UMETA(DisplayName = "无消耗"),
	INNER_FORCE UMETA(DisplayName = "内力消耗"),
	STAMINA UMETA(DisplayName = "体力消耗"),
	ACTION_POINTS UMETA(DisplayName = "行动力消耗"),
	HP UMETA(DisplayName = "生命值消耗"),
	ITEM UMETA(DisplayName = "消耗道具/暗器数"),
};
