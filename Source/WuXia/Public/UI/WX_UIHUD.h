// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Event/WX_EventSystem.h"
#include "GameFramework/HUD.h"
#include "WX_UIHUD.generated.h"

/**
 * 
 */
UCLASS()
class WUXIA_API AWX_UIHUD : public AHUD
{
	GENERATED_BODY()
protected:
	

	/** If true, the HUD will draw the selection box */
	bool bDrawBox = false;

	/** Starting coords of the selection box */
	FVector2D BoxStart;

	/** Width and height of the selection box */
	FVector2D BoxSize;

	/** Current position of the selection box */
	FVector2D BoxCurrentPosition;

	/** Color of the selection box */
	UPROPERTY(EditAnywhere, Category="UI")
	FLinearColor SelectionBoxColor;

public:

	UPROPERTY(EditAnywhere, Category="UI")
	TSubclassOf<UUserWidget> GameMainUIPannel=nullptr;
	
	/** Initialization */
	virtual void BeginPlay() override;

	/** Updates the drag selection box */
	void DragSelectUpdate(FVector2D Start, FVector2D WidthAndHeight, FVector2D CurrentPosition, bool bDraw);
	
	UWX_EventSystem* EventSystem;
	
protected:

	/** Draws the HUD */
	virtual void DrawHUD() override;
};
