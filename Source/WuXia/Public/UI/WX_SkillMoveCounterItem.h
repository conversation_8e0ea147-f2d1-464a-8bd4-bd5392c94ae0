// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "WX_SkillMoveCounterItem.generated.h"

struct FCharacterSkillMove;
class AWX_UIHUD;
/**
 * 
 */
UCLASS()
class WUXIA_API UWX_SkillMoveCounterItem : public UUserWidget
{
	GENERATED_BODY()

	virtual void NativeConstruct() override;

public:

	UPROPERTY(meta=(BindWidget))
	class UTextBlock* SkillMoveName;

	UPROPERTY(meta=(BindWidget))
	class UTextBlock* Counter;

	UPROPERTY(EditDefaultsOnly,BlueprintReadWrite)
	FText SkillMoveNameText;

	UPROPERTY(EditDefaultsOnly,BlueprintReadWrite)
	FText SkillMoveCounterText;

	AWX_UIHUD* HUD;
	
	FCharacterSkillMove* SkillMove;

	int32 index=0;
	
	UFUNCTION()
	void UpdateCounter(class UWX_EventPayload* Payload);
};
