

#pragma once

#include "CoreMinimal.h"
#include "Interface/BPI_CharacterInterface.h"
#include "Interface/BPI_CombatBehaviorInterface.h"
#include "WuXia/WuXiaPlayerController.h"
#include "WX_CharacterController.generated.h"

class AWX_BattleMag;
class UWX_FollowSystemComponent;
//每次角色运行完自己回合需要发消息
DECLARE_DYNAMIC_DELEGATE_OneParam(FONNextTurnDelegate, AWX_PlayerCharacterUnit*, CurrentUnit);
/**
 * 
 */
UCLASS()
class WUXIA_API AWX_CharacterController : public AWuXiaPlayerController
{
	GENERATED_BODY()

	AWX_CharacterController();

	virtual void BeginPlay() override;	
	
public:
	
	FONNextTurnDelegate ONNextTurn;

	UPROPERTY(EditAnywhere, Category = "Input")
	UInputAction* SelectUpDirectionAction;

	UPROPERTY(EditAnywhere, Category = "Input")
	UInputAction* SelectDownDirectionAction;

	UPROPERTY(EditAnywhere, Category = "Input")
	UInputAction* SelectLeftDirectionAction;

	UPROPERTY(EditAnywhere, Category = "Input")
	UInputAction* SelectRightDirectionAction;

	UPROPERTY(EditAnywhere, Category = "Input")
	UInputAction* SelectMiddleDirectionAction;

#pragma region 战斗相关

protected:
	
	virtual void SetupInputComponent() override;
	
	void SelectUpDirectionStarted(const FInputActionValue& InputActionValue);

	void SelectDownDirectionStarted(const FInputActionValue& InputActionValue);

	void SelectLeftDirectionStarted(const FInputActionValue& InputActionValue);
	
	void SelectRightDirectionStarted(const FInputActionValue& InputActionValue);
	
	void SelectMiddleDirectionStarted(const FInputActionValue& InputActionValue);

	
public:
	
	//获取当前角色
	UFUNCTION(BlueprintCallable, Category = "Character")
	AWX_PlayerCharacterUnit* GetCurrentCharacter() const { return TargetUnit; }

	//释放技能
	UFUNCTION()
	void ReleaseSkill();

	//通过摇杆或者方向键+A。释放上中下招式
	UFUNCTION()
	void ReleaseSkillByDirection();

	//反击
	UFUNCTION()
	void CounterAttack();
	
#pragma endregion 	
};


