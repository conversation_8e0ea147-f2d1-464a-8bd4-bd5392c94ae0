#pragma once

#include "CoreMinimal.h"
#include "AIController.h"
#include "WX_CharacterUnitBase.h"
#include "Event/GlobalEventMag.h"
#include "WX_PlayerCharacterUnit.generated.h"

class AWX_CharacterController;
class USphereComponent;

/** Delegate to report that this unit has finished moving */
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnUnitMoveCompletedDelegate, AWX_PlayerCharacterUnit*, Unit);


UCLASS(Abstract)
class WUXIA_API AWX_PlayerCharacterUnit : public AWX_CharacterUnitBase
{
	GENERATED_BODY()
	
public:
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Movement Preview", meta = (AllowPrivateAccess = "true"))
	class USplineComponent* MovementPreviewSpline;

	//abilitysystemcomp
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Movement Preview", meta = (AllowPrivateAccess = "true"))
	class UAbilitySystemComponent* AbilitySystemComponent;

	/** Interaction range sphere */
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components", meta = (AllowPrivateAccess = "true"))
	USphereComponent* InteractionRange;

	//是否是当前被选择角色
	UPROPERTY()
	bool IsBeenSelected=false;
	
protected:

	/** Cast reference to the AI Controlling this unit */
	TObjectPtr<AAIController> AIController;

	virtual void NotifyControllerChanged() override;
	
public:
	// Sets default values for this character's properties
	AWX_PlayerCharacterUnit();
	
protected:
	
	virtual void BeginPlay() override;
	// Called every frame
	virtual void Tick(float DeltaTime) override;
	
public:

	UPROPERTY()
	AWX_CharacterController * PlayerController;
	
	// 定义颜色
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Preview", meta = (AllowPrivateAccess = "true"))// 可到达路径颜色（绿色）
	FLinearColor ReachableColor = FLinearColor::Green;
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Preview", meta = (AllowPrivateAccess = "true"))
	FLinearColor UnreachableColor = FLinearColor::Red;   // 不可到达路径颜色（红色）
	
	
	//预览线的粗细
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Preview", meta = (AllowPrivateAccess = "true"))
	float PreviewLineThickness = 3.0f;
	// 路径预览
	void UpdatePreviewPath(const FVector& MouseWorldLocation);
	//清除预览
	void ClearPreviewPath();
	//绘制预览
	void DrawPreviewPath();
	
	// 鼠标位置获取
	bool GetMouseWorldLocation(FVector& WorldLocation, FVector& WorldDirection);
	bool GetGroundLocationUnderCursor(FVector& GroundLocation);


#pragma region 移动相关

	/** Stops unit movement immediately */
	void StopMoving();
	/** Notifies this unit that it was selected */
	UFUNCTION()
	void UnitSelected();

	/** Notifies this unit that it was deselected */
	void UnitDeselected();

	/** Notifies this unit that it's been interacted with by another actor */
	void Interact(AWX_PlayerCharacterUnit* Interactor);

	/** Attempts to move this unit to its */
	bool MoveToLocation(const FVector& Location, float AcceptanceRadius);

protected:

	/** called by the AI controller when this unit has finished moving */
	void OnMoveFinished(FAIRequestID RequestID, const FPathFollowingResult& Result);
	
	/** Blueprint handler for strategy game selection */
	UFUNCTION(BlueprintImplementableEvent, Category="NPC", meta=(DisplayName="Unit Selected"))
	void BP_UnitSelected();

	/** Blueprint handler for strategy game deselection */
	UFUNCTION(BlueprintImplementableEvent, Category="NPC", meta=(DisplayName="Unit Deselected"))
	void BP_UnitDeselected();

	/** Blueprint handler for strategy game interactions */
	UFUNCTION(BlueprintImplementableEvent, Category="NPC", meta=(DisplayName="Interaction Behavior"))
	void BP_InteractionBehavior(AWX_PlayerCharacterUnit* Interactor);

#pragma endregion

public:
	
	FOnUnitMoveCompletedDelegate OnMoveCompleted;

	
	
};
