// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "CharacterStruct/WX_CharacterAttribute.h"
#include "GameFramework/Character.h"
#include "Interface/BPI_CombatBehaviorInterface.h"
#include "WX_CharacterUnitBase.generated.h"

class UWX_EventSystem;
class UWX_EventPayload;

UCLASS()
class WUXIA_API AWX_CharacterUnitBase : public ACharacter,public IBP_CombatBehaviorInterface
{
	GENERATED_BODY()

public:
	// Sets default values for this character's properties
	AWX_CharacterUnitBase();

	UPROPERTY(EditDefaultsOnly, BlueprintReadWrite, Category = Setup)
	ECharacterState CharacterState=ECharacterState::Exploration;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Character")
	ECharacterType CharacterType=ECharacterType::Enemy;

	bool isInBattle=false;

	//人物属性结构体
	FWX_CharacterAttribute CharacterAttribute;
	
	//当前拥有行动点
	int32 CurrentActionPoints=CharacterAttribute.ActionPoints;
	//所需要的行动点
	int32 NeedActionPoints;
	//可到达的目标点
	FVector CanReachTargetLocation;
	//预览路线点
	TArray<FVector> PathPoints;
	
	//回合是否死亡标记
	UPROPERTY(BlueprintReadOnly, Category = "Combat", meta = (AllowPrivateAccess = "true"))
	bool bIsDead = false;
	
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;

	UFUNCTION(BlueprintCallable, Category = "Selection")
	void SetSelectedHighLight(bool bSelected);

	UFUNCTION()
	void OnCursorBegin(AActor* TouchedActor);
	UFUNCTION()
	void OnCursorEnd(AActor* TouchedActor);

	TArray<FVector> FindPath(const FVector& Start, const FVector& End);

	// 生成细分路径点，在NavMesh路径点之间插入中间点
	UFUNCTION(BlueprintCallable, Category = "Path")
	TArray<FVector> GenerateDetailedPath(const TArray<FVector>& NavMeshPoints);

	//行动力换算变量，1 AP = 100 units
	float ActionPointToUnitConversion = 100.0f;
	//可移动距离
	float GetMaxMovementDistance() const
	{
		return CurrentActionPoints * ActionPointToUnitConversion;
	}
	
	//行动点距离的换算
	int32 CalculateActionPointsForDistance(float Distance) const
	{
		float EffectiveSpeed= ActionPointToUnitConversion;
		return FMath::CeilToInt(Distance / EffectiveSpeed);
	}

	//获取可到达的行动点
	UFUNCTION(BlueprintCallable, Category = "Path")
	int32 GetMaxReachableActionPoints(TArray<FVector> TempPathPoints);

	//当前消耗的行动点
	int32 MaxReachableActionPoints;

	//当前战斗队列索引
	int32 CurrentBattleIndex;

	//伤害逻辑
	UFUNCTION(BlueprintCallable, Category = "Combat")
	void TakeDamage(float DamageAmount);
protected:

	UPROPERTY()
	TSoftObjectPtr<UWX_EventPayload> Payload;
	UPROPERTY()
	TSoftObjectPtr<UWX_EventSystem> EventSystem;
public:
	// Called every frame
	virtual void Tick(float DeltaTime) override;

	// Called to bind functionality to input
	virtual void SetupPlayerInputComponent(class UInputComponent* PlayerInputComponent) override;
};
