// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Character/WX_CharacterUnitBase.h"
#include "CharacterStruct/WX_CharacterAttribute.h"
#include "WX_EnemyUnit.generated.h"

class UDataTableDataMag;
class UWX_SkillInfoItem;
class UWX_EnemyCurrentBeAtkedSkillInfo;

UCLASS()
class WUXIA_API AWX_EnemyUnit : public AWX_CharacterUnitBase
{
	GENERATED_BODY()

public:
	// Sets default values for this character's properties
	AWX_EnemyUnit();

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	class UWidgetComponent* EnemySkillInfoWidget;

	UWX_EnemyCurrentBeAtkedSkillInfo* EnemySkillInfo;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI")
	TSubclassOf<UUserWidget> SkillInfoItemClass;
	
protected:
	
	void UpdateDataTable(UWX_EventPayload* Uwx_EventPayload);
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;

	TArray<int32> CurrentSelectionSkillID;
	TArray<ESkillMoveDirectionType> CurrentSelectionSkillMoves;

	TSoftObjectPtr<UDataTableDataMag> DataTableDataMag;
public:

	//GetCurrentSelectionSkillMoves
	UFUNCTION(BlueprintCallable, Category = "Skill")
	TArray<ESkillMoveDirectionType> GetCurrentSelectionSkillMoves() const { return CurrentSelectionSkillMoves; }
	
	// Called to bind functionality to input
	virtual void SetupPlayerInputComponent(class UInputComponent* PlayerInputComponent) override;

	// AI相关方法

	// 检查是否可以执行攻击
	UFUNCTION(BlueprintCallable, Category = "AI Combat")
	bool CanPerformAttack(class AWX_CharacterUnitBase* Target, int32 ActionPointCost = 1);

	// 执行攻击动作
	UFUNCTION(BlueprintCallable, Category = "AI Combat")
	void PerformAttackAction(class AWX_CharacterUnitBase* Target);

	// 检查能到达的最远位置
	UFUNCTION(BlueprintCallable, Category = "AI Movement")
	void CanReachMaxLocation(const FVector& TargetPosition);

	// 获取到指定位置的最佳路径
	UFUNCTION(BlueprintCallable, Category = "AI Movement")
	TArray<FVector> GetPathToPosition(const FVector& TargetPosition);

	// 消耗行动点
	UFUNCTION(BlueprintCallable, Category = "AI")
	bool ConsumeActionPoints(int32 Points);

	// 检查是否有足够的行动点
	UFUNCTION(BlueprintCallable, Category = "AI")
	bool HasEnoughActionPoints(int32 RequiredPoints) const;

	// 获取当前状态信息（用于AI决策）
	UFUNCTION(BlueprintCallable, Category = "AI")
	FString GetStatusInfo() const;

	//处于战斗状态，显示skillinfo UI，并读取配置表随机分配战斗资源
	UFUNCTION(BlueprintCallable, Category = "AI")
	void CombatState();

#pragma region 战斗接口
	virtual void StartTurn_Implementation() override;
	virtual void EndTurn_Implementation() override;
	virtual bool CanPerformAction_Implementation(int32 ActionCost) const override;
	virtual void PerformAttack_Implementation(AActor* Target, const FGameplayTag& AttackType) override;
#pragma endregion
};
