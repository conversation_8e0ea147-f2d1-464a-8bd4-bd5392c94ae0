// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameplayTagContainer.h"
#include "Subsystems/GameInstanceSubsystem.h"
#include "WX_EventSystem.generated.h"

// 事件类型枚举，可以根据游戏需要扩展
UENUM(BlueprintType)
enum class EEventType : uint8
{
	None = 0,
	PlayerAction,    // 玩家动作相关事件
	Combat,          // 战斗相关事件
	Environment,     // 环境交互相关事件
	Quest,           // 任务相关事件
	UI,              // UI相关事件
	Gameplay,        // 游戏玩法相关事件
	Custom           // 自定义事件（需要提供额外的事件ID）
};


// 事件载荷基类，所有事件数据都继承自此类
UCLASS(BlueprintType)
class WUXIA_API UWX_EventPayload : public UObject
{
	GENERATED_BODY()

public:
	UWX_EventPayload() : EventId(FGameplayTag::EmptyTag) {}
    
	// 事件ID，用于进一步区分同一类型下的不同事件
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Event")
	FGameplayTag EventId;
    
	// 事件类型
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Event")
	EEventType EventType = EEventType::None;
    
	// 事件来源 (可选)
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Event")
	TWeakObjectPtr<UObject> EventSource;
};


// 事件委托声明
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnEventReceived, UWX_EventPayload*, Payload);

// 蓝图事件处理接口 - 任何需要接收事件的蓝图类都应该实现此接口
UINTERFACE(MinimalAPI, Blueprintable)
class UWX_EventReceiverInterface : public UInterface
{
	GENERATED_BODY()
};

class WUXIA_API IWX_EventReceiverInterface
{
	GENERATED_BODY()

public:
	// 蓝图可实现的事件处理函数
	UFUNCTION(BlueprintNativeEvent, Category = "Events")
	void OnEventReceived(UWX_EventPayload* Payload);
};


// 事件处理基类 - 所有事件处理对象都从这个类继承
UCLASS()
class WUXIA_API UWX_EventHandlerBase : public UObject
{
	GENERATED_BODY()

public:
	UWX_EventHandlerBase();

	// 事件处理函数 - 直接在基类中实现
	UFUNCTION()
	virtual void HandleEvent(UWX_EventPayload* Payload);

	// 事件持有者（可以是任何UObject）
	UPROPERTY()
	UObject* Owner;

	// 使用更安全的方式存储函数指针
	TArray<uint8> FunctionPtrData;

	// 存储类信息
	UClass* OwnerClass;

	// 初始化处理器（通用版本）
	template<class UserClass>
	void Initialize(UserClass* InOwner, void (UserClass::*InFunction)(UWX_EventPayload*));
};

UCLASS()
class WUXIA_API UWX_EventSystem : public UGameInstanceSubsystem
{
	GENERATED_BODY()

private:
	 
	// 事件处理器映射表，按事件类型分类
	TMap<EEventType, TArray<UWX_EventHandlerBase*>> EventTypeHandlers;
    
	// 事件处理器映射表，按事件ID分类
	TMap<FGameplayTag, TArray<UWX_EventHandlerBase*>> EventIdHandlers;
    
	// 事件对象池，减少频繁的内存分配
	TArray<UWX_EventPayload*> EventPool;
    
	// 对象池大小限制
	static constexpr int32 MaxPoolSize = 100;

	// 记录对象注册的事件处理器，用于快速注销
	TMap<UObject*, TArray<UWX_EventHandlerBase*>> ObjectHandlerMap;
    
	// 存储蓝图事件接收器 (按类型)
	TMap<EEventType, TArray<TWeakObjectPtr<UObject>>> BlueprintTypeReceivers;
    
	// 存储蓝图事件接收器 (按ID)
	TMap<FGameplayTag, TArray<TWeakObjectPtr<UObject>>> BlueprintIdReceivers;
	
public:

	// 注册事件监听（按事件类型）- 通用模板版本
	template<class UserClass>
	void RegisterEventByType(EEventType EventType, UserClass* Object, void (UserClass::*Function)(UWX_EventPayload*));
    
	// 注册事件监听（按事件ID）- 通用模板版本
	template<class UserClass>
	void RegisterEventById(FGameplayTag EventId, UserClass* Object, void (UserClass::*Function)(UWX_EventPayload*));
    
	// 注销事件监听（按事件类型）
	void UnregisterEventByType(EEventType EventType, UObject* Object);
    
	// 注销事件监听（按事件ID）
	void UnregisterEventById(FGameplayTag EventId, UObject* Object);
    
	// 注销对象的所有事件监听
	void UnregisterAllEvents(UObject* Object);
    
	// 触发事件
	UFUNCTION(BlueprintCallable, Category = "Events")
	void TriggerEvent(UWX_EventPayload* Payload);
    
	// 创建事件载荷对象（从对象池获取或新建）
	UFUNCTION(BlueprintCallable, Category = "Events")
	UWX_EventPayload* CreateEventPayload(EEventType EventType, FGameplayTag EventId);
    
	// 回收事件载荷对象到对象池
	void RecycleEventPayload(UWX_EventPayload* Payload);
    
	// 清理事件系统
	void Cleanup();
    
	// 蓝图友好的事件注册接口（按类型）- 使用接口而不是委托
	UFUNCTION(BlueprintCallable, Category = "Events")
	void BP_RegisterEventByType(EEventType EventType, UObject* Receiver);
    
	// 蓝图友好的事件注册接口（按ID）- 使用接口而不是委托
	UFUNCTION(BlueprintCallable, Category = "Events")
	void BP_RegisterEventById(FGameplayTag EventId, UObject* Receiver);
    
	// 蓝图友好的事件注销接口（按类型）
	UFUNCTION(BlueprintCallable, Category = "Events")
	void BP_UnregisterEventByType(EEventType EventType, UObject* Receiver);
    
	// 蓝图友好的事件注销接口（按ID）
	UFUNCTION(BlueprintCallable, Category = "Events")
	void BP_UnregisterEventById(FGameplayTag EventId, UObject* Receiver);
    
	// 蓝图友好的注销所有事件接口
	UFUNCTION(BlueprintCallable, Category = "Events")
	void BP_UnregisterAllEvents(UObject* Receiver);
private:
	// 创建一个事件处理器
	template<class UserClass>
	UWX_EventHandlerBase* CreateEventHandler(UserClass* Object, void (UserClass::*Function)(UWX_EventPayload*));
};

// 创建一个事件处理器
template<class UserClass>
UWX_EventHandlerBase* UWX_EventSystem::CreateEventHandler(UserClass* Object, void (UserClass::*Function)(UWX_EventPayload*))
{
    // 创建事件处理器
    UWX_EventHandlerBase* Handler = NewObject<UWX_EventHandlerBase>();
    Handler->Initialize(Object, Function);
    
    // 记录处理器，以便后续清理
    TArray<UWX_EventHandlerBase*>& HandlerArray = ObjectHandlerMap.FindOrAdd(Object);
    HandlerArray.Add(Handler);
    
    return Handler;
}

// 事件处理基类初始化模板实现
template<class UserClass>
void UWX_EventHandlerBase::Initialize(UserClass* InOwner, void (UserClass::*InFunction)(UWX_EventPayload*))
{
    Owner = InOwner;
    // 使用TArray<uint8>安全存储函数指针
    typedef void (UserClass::*FuncPtrType)(UWX_EventPayload*);

    // 清空并重新分配存储空间
    FunctionPtrData.Empty();
    FunctionPtrData.AddUninitialized(sizeof(FuncPtrType));

    // 安全地复制函数指针数据
    FMemory::Memcpy(FunctionPtrData.GetData(), &InFunction, sizeof(FuncPtrType));
    OwnerClass = UserClass::StaticClass();

    UE_LOG(LogTemp, Warning, TEXT("初始化处理器：所有者 %s，类型 %s"),
            *Owner->GetName(), *OwnerClass->GetName());
}

// 事件处理基类构造函数实现
inline UWX_EventHandlerBase::UWX_EventHandlerBase()
    : Owner(nullptr), OwnerClass(nullptr)
{
}

// 事件处理基类的HandleEvent方法实现
inline void UWX_EventHandlerBase::HandleEvent(UWX_EventPayload* Payload)
{
    if (!Owner || FunctionPtrData.Num() == 0 || !OwnerClass)
    {
        if (!Owner)
            UE_LOG(LogTemp, Error, TEXT("事件处理失败：所有者为空"));
        if (FunctionPtrData.Num() == 0)
            UE_LOG(LogTemp, Error, TEXT("事件处理失败：函数指针为空"));
        if (!OwnerClass)
            UE_LOG(LogTemp, Error, TEXT("事件处理失败：类型信息为空"));
        return;
    }
    
    // 添加调试日志
    UE_LOG(LogTemp, Warning, TEXT("尝试处理事件，所有者：%s, 实际类型：%s, 期望类型：%s"), 
        *Owner->GetName(), *Owner->GetClass()->GetName(), *OwnerClass->GetName());
    
    // 检查类型兼容性
    if (Owner->IsA(OwnerClass))
    {
        UE_LOG(LogTemp, Warning, TEXT("类型匹配，执行回调函数"));
        
        // 安全方式：从TArray<uint8>中恢复函数指针
        typedef void (UObject::*GenericFuncPtr)(UWX_EventPayload*);
        GenericFuncPtr SafeFunc;

        // 从存储的数据中恢复函数指针
        if (FunctionPtrData.Num() >= sizeof(GenericFuncPtr))
        {
            FMemory::Memcpy(&SafeFunc, FunctionPtrData.GetData(), sizeof(GenericFuncPtr));
        }
        else
        {
            UE_LOG(LogTemp, Error, TEXT("函数指针数据大小不匹配"));
            return;
        }
        
        // 调用函数
        UE_LOG(LogTemp, Warning, TEXT("调用处理函数"));
        (Owner->*SafeFunc)(Payload);
        UE_LOG(LogTemp, Warning, TEXT("处理函数执行完成"));
    }
    else
    {
        // 类型不匹配的日志
        UE_LOG(LogTemp, Error, TEXT("类型不匹配！期望类型：%s，实际类型：%s"), 
            *OwnerClass->GetName(), *Owner->GetClass()->GetName());
    }
}

template<class UserClass>
void UWX_EventSystem::RegisterEventByType(EEventType EventType, UserClass* Object, void (UserClass::*Function)(UWX_EventPayload*))
{
    if (!Object)
        return;
    
    // 创建事件处理器
    UWX_EventHandlerBase* Handler = CreateEventHandler(Object, Function);
    
    // 添加到事件类型处理器映射表
    TArray<UWX_EventHandlerBase*>& HandlerArray = EventTypeHandlers.FindOrAdd(EventType);
    HandlerArray.Add(Handler);
    
    UE_LOG(LogTemp, Warning, TEXT("注册事件类型 [%d] 完成，对象: %s，处理器数量: %d"), 
        static_cast<int32>(EventType), *Object->GetName(), HandlerArray.Num());
}

template<class UserClass>
void UWX_EventSystem::RegisterEventById(FGameplayTag EventId, UserClass* Object, void (UserClass::*Function)(UWX_EventPayload*))
{
    if (!Object || EventId == FGameplayTag::EmptyTag)
        return;
    
    UE_LOG(LogTemp, Warning, TEXT("注册事件ID [%s] 开始，对象: %s，类型: %s"), 
        *EventId.ToString(), *Object->GetName(), *Object->GetClass()->GetName());

   
    // 创建事件处理器
    UWX_EventHandlerBase* Handler = CreateEventHandler(Object, Function);
    //打印handler名字
    UE_LOG(LogTemp, Warning, TEXT("handler名字：%s"), *Handler->GetName());
    // 添加到事件ID处理器映射表
    TArray<UWX_EventHandlerBase*>& HandlerArray = EventIdHandlers.FindOrAdd(EventId);
    HandlerArray.Add(Handler);
    
    UE_LOG(LogTemp, Warning, TEXT("注册事件ID [%s] 完成，对象: %s，处理器数量: %d"), 
        *EventId.ToString(), *Object->GetName(), HandlerArray.Num());
}