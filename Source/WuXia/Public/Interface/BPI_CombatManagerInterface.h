// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/Interface.h"
#include "GameplayTagContainer.h"
#include "BPI_CombatManagerInterface.generated.h"

class AWX_BattleMag;
class UDataTableDataMag;
// 战斗状态枚举
UENUM(BlueprintType)
enum class ECombatState : uint8
{
	None = 0,
	Preparing,		// 准备阶段
	InProgress,		// 战斗进行中
	PlayerTurn,		// 玩家回合
	EnemyTurn,		// 敌方回合
	Paused,			// 暂停
	Ending,			// 结束阶段
	Completed		// 已完成
};

// 战斗结果枚举
UENUM(BlueprintType)
enum class ECombatResult : uint8
{
	None = 0,
	Victory,		// 胜利
	Defeat,			// 失败
	Draw,			// 平局
	Escaped,		// 逃跑
	Interrupted		// 被中断
};

// 回合信息结构体
USTRUCT(BlueprintType)
struct WUXIA_API FTurnInfo
{
	GENERATED_BODY()

	// 当前行动的角色
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Turn")
	TWeakObjectPtr<AActor> CurrentActor;

	// 回合数
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Turn")
	int32 TurnNumber = 1;

	// 当前角色在队列中的索引
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Turn")
	int32 CurrentIndex = 0;

	// 是否是玩家回合
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Turn")
	bool bIsPlayerTurn = false;
};

// This class does not need to be modified.
UINTERFACE(MinimalAPI, Blueprintable)
class UBP_CombatManagerInterface : public UInterface
{
	GENERATED_BODY()
};

/**
 * 战斗管理器接口 - 定义战斗管理器必须实现的核心功能
 * 包括战斗流程控制、回合管理、参战单位管理等
 */
class WUXIA_API IBP_CombatManagerInterface
{
	GENERATED_BODY()

public:
	/**
	 * 开始战斗
	 * @param PlayerUnits 玩家参战单位
	 * @param EnemyUnits 敌方参战单位
	 * @return 是否成功开始战斗
	 */
	UFUNCTION(BlueprintCallable, BlueprintNativeEvent, Category = "Combat Manager")
	bool StartCombat(const TArray<AActor*>& PlayerUnits, const TArray<AActor*>& EnemyUnits);

	/**
	 * 结束战斗
	 * @param Result 战斗结果
	 */
	UFUNCTION(BlueprintCallable, BlueprintNativeEvent, Category = "Combat Manager")
	void EndCombat(ECombatResult Result);

	/**
	 * 暂停战斗
	 */
	UFUNCTION(BlueprintCallable, BlueprintNativeEvent, Category = "Combat Manager")
	void PauseCombat();

	/**
	 * 恢复战斗
	 */
	UFUNCTION(BlueprintCallable, BlueprintNativeEvent, Category = "Combat Manager")
	void ResumeCombat();

	/**
	 * 下一个回合
	 */
	UFUNCTION(BlueprintCallable, BlueprintNativeEvent, Category = "Combat Manager")
	void NextTurn();

	/**
	 * 获取当前战斗状态
	 * @return 当前战斗状态
	 */
	UFUNCTION(BlueprintCallable, BlueprintNativeEvent, Category = "Combat Manager")
	ECombatState GetCombatState() const;

	/**
	 * 获取当前回合信息
	 * @return 当前回合信息
	 */
	UFUNCTION(BlueprintCallable, BlueprintNativeEvent, Category = "Combat Manager")
	FTurnInfo GetCurrentTurnInfo() const;

	/**
	 * 获取所有参战单位
	 * @return 所有参战单位数组
	 */
	UFUNCTION(BlueprintCallable, BlueprintNativeEvent, Category = "Combat Manager")
	TArray<AActor*> GetAllCombatUnits() const;

	/**
	 * 获取玩家单位
	 * @return 玩家单位数组
	 */
	UFUNCTION(BlueprintCallable, BlueprintNativeEvent, Category = "Combat Manager")
	TArray<AActor*> GetPlayerUnits() const;

	/**
	 * 获取敌方单位
	 * @return 敌方单位数组
	 */
	UFUNCTION(BlueprintCallable, BlueprintNativeEvent, Category = "Combat Manager")
	TArray<AActor*> GetEnemyUnits() const;

	/**
	 * 获取存活的单位
	 * @param bPlayerUnits 是否获取玩家单位（false为敌方单位）
	 * @return 存活单位数组
	 */
	UFUNCTION(BlueprintCallable, BlueprintNativeEvent, Category = "Combat Manager")
	TArray<AActor*> GetAliveUnits(bool bPlayerUnits) const;

	/**
	 * 添加参战单位
	 * @param Unit 要添加的单位
	 * @param bIsPlayerUnit 是否为玩家单位
	 */
	UFUNCTION(BlueprintCallable, BlueprintNativeEvent, Category = "Combat Manager")
	void AddCombatUnit(AActor* Unit, bool bIsPlayerUnit);

	/**
	 * 移除参战单位
	 * @param Unit 要移除的单位
	 */
	UFUNCTION(BlueprintCallable, BlueprintNativeEvent, Category = "Combat Manager")
	void RemoveCombatUnit(AActor* Unit);

	/**
	 * 检查战斗是否结束
	 * @return 战斗结果，如果战斗未结束返回None
	 */
	UFUNCTION(BlueprintCallable, BlueprintNativeEvent, Category = "Combat Manager")
	ECombatResult CheckCombatEnd() const;

	/**
	 * 初始化回合顺序
	 */
	UFUNCTION(BlueprintCallable, BlueprintNativeEvent, Category = "Combat Manager")
	void InitializeTurnOrder();

	/**
	 * 获取回合顺序
	 * @return 按回合顺序排列的单位数组
	 */
	UFUNCTION(BlueprintCallable, BlueprintNativeEvent, Category = "Combat Manager")
	TArray<AActor*> GetTurnOrder() const;

	/**
	 * 跳过当前回合
	 */
	UFUNCTION(BlueprintCallable, BlueprintNativeEvent, Category = "Combat Manager")
	void SkipCurrentTurn();

	/**
	 * 处理单位死亡
	 * @param DeadUnit 死亡的单位
	 */
	UFUNCTION(BlueprintCallable, BlueprintNativeEvent, Category = "Combat Manager")
	void HandleUnitDeath(AActor* DeadUnit);

	/**
	 * 获取当前行动单位
	 * @return 当前应该行动的单位
	 */
	UFUNCTION(BlueprintCallable, BlueprintNativeEvent, Category = "Combat Manager")
	AActor* GetCurrentActiveUnit() const;

	/**
	 * 检查是否为玩家回合
	 * @return 是否为玩家回合
	 */
	UFUNCTION(BlueprintCallable, BlueprintNativeEvent, Category = "Combat Manager")
	bool IsPlayerTurn() const;

	/**
	 * 强制结束当前单位的回合
	 */
	UFUNCTION(BlueprintCallable, BlueprintNativeEvent, Category = "Combat Manager")
	void ForceEndCurrentTurn();

	/**
	 * 获取战斗管理器
	 * @return 战斗管理器实例
	 */
	UFUNCTION(BlueprintCallable, BlueprintNativeEvent, Category = "Manager Provider")
	AWX_BattleMag* GetBattleManager() ;

	/**
	 * 获取事件系统
	 * @return 事件系统实例
	 */
	UFUNCTION(BlueprintCallable, BlueprintNativeEvent, Category = "Manager Provider")
	UWX_EventSystem* GetEventSystem() ;

	/**
	 * 获取数据表管理器
	 * @return 数据表管理器实例
	 */
	UFUNCTION(BlueprintCallable, BlueprintNativeEvent, Category = "Manager Provider")
	UDataTableDataMag* GetDataTableManager();
};
