// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/Object.h"
#include "DataTableDataMag.generated.h"

/**
 * 
 */
UCLASS()
class WUXIA_API UDataTableDataMag : public UGameInstanceSubsystem
{
	GENERATED_BODY()

public:

	//加载所有表格
	void LoadAllTable();

	//通过表格名字获取表格
	void GetDataTable(const FName TableName);
	
	//通过表格名字获取表格
	UDataTable* GetDataTableByName(const FName Name);
	//获得rowname
	FName GetRowName(const FName Name);

	template<typename T>
	T* GetRandomRowByName(const FName& TableName)
	{
		//读取词条表格
		UDataTable* dataTable = GetDataTableByName(TableName);
		if (dataTable!= nullptr)
		{
			//获取所有行名
			TArray<FName> RowNames=dataTable->GetRowNames();
			//随机获取一行
			int32 RandomIndex=FMath::RandRange(0,RowNames.Num()-1);
			//获取行名
			FName RowName=RowNames[RandomIndex];
			// GEngine->AddOnScreenDebugMessage(-1,5.f,FColor::Red,FString::Printf(TEXT("PW_itemBase.cpp RowName:%s"),*RowName.ToString()));
			
			return dataTable->FindRow<T>(RowName, TEXT(""));
		}
		else
		{
			return nullptr;
		}
	}

	//
	template<typename T>
	void RandomizeInitializeAttribute(T* dataTable);

	
private:
	//缓存加载的表格
	TMap<FName, UDataTable*> LoadedDataTables;
};