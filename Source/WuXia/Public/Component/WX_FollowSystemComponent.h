// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "WX_FollowSystemComponent.generated.h"


class AWX_PlayerCharacterUnit;

UCLASS(ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class WUXIA_API UWX_FollowSystemComponent : public UActorComponent
{
	GENERATED_BODY()

public:
	// Sets default values for this component's properties
	UWX_FollowSystemComponent();

protected:
	// Called when the game starts
	virtual void BeginPlay() override;

	virtual void TickComponent(float DeltaTime, enum ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

public:

	UPROPERTY()
	TArray<AActor*> CharacterTeamUnits;

	UPROPERTY(BlueprintReadOnly, Category = "Team")
	AWX_PlayerCharacterUnit* CurrentLeader;

	// 跟随参数
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Follow Settings")
	float FollowDistance = 200.0f;
    
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Follow Settings")
	float SideOffset = 150.0f;
    
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Follow Settings")
	float StopDistance = 120.0f;

	// 选择新的队长,并且更新新的跟随队伍，当通过Click选择到人物的时候，设置当前选择到的角色为队长
	UFUNCTION(BlueprintCallable, Category = "Team")
	void SetTeamLeader(AWX_PlayerCharacterUnit* NewLeader);

	//更新跟随位置，选择到队长的时候，执行一次，每一次移动的时候进行执行一次
	UFUNCTION(BlueprintCallable, Category = "Team")
	void UpdateFollowPositions();

	// 计算跟随位置
	UFUNCTION(BlueprintCallable, Category = "Team")
	FVector CalculateFollowPosition(AWX_PlayerCharacterUnit* Leader, bool bIsLeftSide);

	//获取队伍成员列表
	UFUNCTION(BlueprintCallable,Category="Team")
	void GetTeamMembers();
   
private:
	//当前跟随者
	TArray<AWX_PlayerCharacterUnit*> Followers;
    
	// 移动跟随者到目标位置
	void MoveFollowerToPosition(AWX_PlayerCharacterUnit* Follower, FVector TargetPosition);
    
	// 获取AI控制器
	class AAIController* GetAIController(AWX_PlayerCharacterUnit* Character);
	
};
