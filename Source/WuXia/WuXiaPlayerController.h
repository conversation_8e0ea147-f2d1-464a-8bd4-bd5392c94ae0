// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "CharacterStruct/WX_CharacterAttribute.h"
#include "Event/GlobalEventMag.h"
#include "WuXiaPlayerController.generated.h"

class AWX_PlayerCharacterUnit;
class AWX_CharacterUnitBase;
class AWX_UIHUD;
class AWuXiaCharacter;

struct FInputActionValue;
/** Forward declaration to improve compiling times */
class UNiagaraSystem;
class UInputMappingContext;
class UInputAction;

DECLARE_LOG_CATEGORY_EXTERN(LogTemplateCharacter, Log, All);

//当碰撞到战区区域，发送获取到的当前区域的所有角色
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnBattleAreaUnitsDelegate, const TArray<AWX_CharacterUnitBase*>&, BattleAreaUnits);

UCLASS()
class AWuXiaPlayerController : public APlayerController
{
	GENERATED_BODY()

public:
	AWuXiaPlayerController();

	virtual void BeginPlay() override;
	
	//跟随
	UPROPERTY(VisibleAnywhere,Category="BattleComp")
	class UWX_FollowSystemComponent* FollowSystemComponentClass;

	/** Time Threshold to know if it was a short press */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = Input)
	float ShortPressThreshold;
	
	virtual void OnPossess(APawn* InPawn) override;
	
	/** FX Class that we will spawn when clicking */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = Input)
	UNiagaraSystem* FXCursor;

	/** 日常探索 */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category=Input, meta=(AllowPrivateAccess = "true"))
	UInputMappingContext* DefaultMappingContext;

	/** 战斗控制器 */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category=Input, meta=(AllowPrivateAccess = "true"))
	UInputMappingContext* BattleMappingContext;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category=Input, meta=(AllowPrivateAccess = "true"))
	UInputAction* MoveCameraAction;
	
	/** Input Action for zooming the camera */
	UPROPERTY(EditAnywhere, Category = "Input")
	UInputAction* ZoomCameraAction;

	/** Input Action for resetting the camera to its default position */
	UPROPERTY(EditAnywhere, Category = "Input")
	UInputAction* ResetCameraAction;

	/** Input Action for select and click */
	UPROPERTY(EditAnywhere, Category = "Input")
	UInputAction* SelectClickAction;

	/** Input Action for select press and hold */
	UPROPERTY(EditAnywhere, Category = "Input")
	UInputAction* SelectHoldAction;

	/** Input Action for click interaction */
	UPROPERTY(EditAnywhere, Category = "Input")
	UInputAction* InteractClickAction;

	/** Input Action for interaction press and hold */
	UPROPERTY(EditAnywhere, Category = "Input")
	UInputAction* InteractHoldAction;

	/** Input Action for modifying selection mode */
	UPROPERTY(EditAnywhere, Category = "Input")
	UInputAction* SelectionModifierAction;
	
	UPROPERTY(EditAnywhere, Category = "Input", meta = (ClampMin = 0, ClampMax = 10000, Units = "cm"))
	float InteractionRadius = 25.0f;

	/** Max distance between the starting and current position of the second touch finger to be considered a box selection */
	UPROPERTY(EditAnywhere, Category = "Input", meta = (ClampMin = 0, ClampMax = 10000))
	float MinSecondFingerDistanceForBoxSelect = 10.0f;

	/** Saves the world location of the last initiated interaction */
	FVector CachedInteraction;

	/** Saves the world location of the last initiated unit selection */
	FVector CachedSelection;

	/** Saves the world location where the player started a press and hold interaction */
	FVector2D StartingInteractionPosition;

	/** Saves the current world location of the player's cursor in press and hold interaction */
	FVector2D CurrentInteractionPosition;

	/** Saves the starting world location of a player's cursor in a press and hold selection box */
	FVector2D StartingSelectionPosition;

	/** Saves the starting location of a two-finger touch interaction (pinch) */
	FVector2D StartingSecondFingerPosition;

	/** Saves the current location of a two-finger touch interaction (pinch) */
	FVector2D CurrentSecondFingerPosition;

	/** Current camera zoom level */
	float CameraZoom;

	/** Default camera zoom level */
	float DefaultZoom;

	/** Minimum allowed camera zoom level */
	UPROPERTY(EditAnywhere, Category = "Camera", meta = (ClampMin = 0, ClampMax = 10000))
	float MinZoomLevel = 1000.0f;

	/** Maximum allowed camera zoom level */
	UPROPERTY(EditAnywhere, Category = "Camera", meta = (ClampMin = 0, ClampMax = 10000))
	float MaxZoomLevel = 2500.0f;

	/** Scales zoom inputs by this value */
	UPROPERTY(EditAnywhere, Category = "Camera", meta = (ClampMin = 0, ClampMax = 1000))
	float ZoomScaling = 100.0f;

	/** Affects how fast the camera moves while dragging with the mouse */
	UPROPERTY(EditAnywhere, Category = "Camera", meta = (ClampMin = 0, ClampMax = 10000))
	float DragMultiplier = 0.1f;

	/** Trace channel to use for selection trace checks */
	UPROPERTY(EditAnywhere, Category = "Selection")
	TEnumAsByte<ETraceTypeQuery> SelectionTraceChannel;

	UPROPERTY(EditAnywhere, Category = "Selection")
	bool bSelectedDebug=true;

	bool bExplorationMode = true;

	/** Currently selected unit */
	UPROPERTY()
	AWX_PlayerCharacterUnit* TargetUnit = nullptr;

	/** Previous selected unit */
	UPROPERTY()
	AWX_PlayerCharacterUnit* PreviousTargetUnit = nullptr;
	
	/** Currently selected unit list */
	UPROPERTY()
	TArray<AWX_PlayerCharacterUnit*> ControlledUnits;

	UPROPERTY()
	EPlayerChacterTeamType PlayerTeamType = EPlayerChacterTeamType::Team;
	
protected:
	
	/** If true, the player is adding or removing units to the selected units list */
	bool bSelectionModifier = false;

	/** If true, allow the player to interact with game objects */
	bool bAllowInteraction = true;

	virtual void SetupInputComponent() override;

	void EnterBattleAndSetupInputBattleMapping(UWX_EventPayload* Payload);

	void EnterExplorationAndSetupInputExplorationMapping(UWX_EventPayload* Payload);

	/** Strategy Pawn associated with this controller */
	TObjectPtr<AWuXiaCharacter> ControlledPawn;
	/** Strategy HUD associated with this controller */
	TObjectPtr<AWX_UIHUD> StrategyHUD;

	
	
#pragma region camera
	/** Moves the camera by the given input */
	void MoveCamera(const FInputActionValue& Value);
	
	/** Changes the camera zoom level by the given input */
	void ZoomCamera(const FInputActionValue& Value);

	/** Resets the camera to its initial value */
	void ResetCamera(const FInputActionValue& Value);
#pragma endregion

#pragma region mouse
	/** Start a select and hold input */
	void SelectHoldStarted(const FInputActionValue& Value);
	
	/** Select and hold input triggered */
	void SelectHoldTriggered(const FInputActionValue& Value);

	/** Select and hold input completed */
	void SelectHoldCompleted(const FInputActionValue& Value);

	/** Select click action */
	void SelectClick(const FInputActionValue& Value);

	/** Presses or releases the selection modifier key */
	void SelectionModifier(const FInputActionValue& Value);

	/** Starts an interaction hold input */
	void InteractHoldStarted(const FInputActionValue& Value);

	void DoDragScrollCommand();
	/** Interaction hold input triggered */
	void InteractHoldTriggered(const FInputActionValue& Value);

	/** Interaction click input started */
	void InteractClickStarted(const FInputActionValue& Value);

	/** Interaction click input completed */
	void InteractClickCompleted(const FInputActionValue& Value);
#pragma endregion mouse
	
public:
	
	/** Updates selected units from the HUD's drag select box */
	void DragSelectUnits(const TArray<AWX_PlayerCharacterUnit*>& Units);
    
	/** Passes the list of selected units */
	const TArray<AWX_PlayerCharacterUnit*>& GetSelectedUnits();
	
	bool GetLocationUnderCursor(FVector& Location);

	/** Calculates and returns the current mouse location */
	FVector2D GetMouseLocation();
	
	/** Spawns the positive cursor effect */
	UFUNCTION(BlueprintImplementableEvent, Category="Cursor", meta=(DisplayName="Cursor Feedback"))
	void BP_CursorFeedback(FVector Location, bool bPositive);

	/** Resets the interaction flag */
	void ResetInteraction();
	
	void DoSelectionCommand();
	
	void DoDeselectAllCommand();
	
	void DoMoveUnitsCommand();

	/** Sorts all controlled units based on their distance to the provided world location */
	AWX_PlayerCharacterUnit* GetClosestSelectedUnitToLocation(FVector TargetLocation);
	
	/** Called when a unit move is completed */
	UFUNCTION()
	void OnMoveCompleted(AWX_PlayerCharacterUnit* MovedUnit);
	
public:
	FOnBattleAreaUnitsDelegate OnBattleAreaUnitsDelegate;
};
