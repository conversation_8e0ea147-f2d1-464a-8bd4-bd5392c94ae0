[/Script/EngineSettings.GameMapsSettings]
GameDefaultMap=/Game/TopDown/Maps/TopDownMap.TopDownMap
EditorStartupMap=/Game/TopDown/Maps/TopDownMap.TopDownMap
GlobalDefaultGameMode="/Script/WuXia.WuXiaGameMode"

[/Script/Engine.RendererSettings]
r.DefaultFeature.AutoExposure.ExtendDefaultLuminanceRange=True
r.DefaultFeature.AutoExposure.ExtendDefaultLuminanceRange=true
r.AllowStaticLighting=False

r.GenerateMeshDistanceFields=True

r.DynamicGlobalIlluminationMethod=1

r.ReflectionMethod=1

r.SkinCache.CompileShaders=True

r.RayTracing=True

r.Shadow.Virtual.Enable=1

r.DefaultFeature.LocalExposure.HighlightContrastScale=0.8

r.DefaultFeature.LocalExposure.ShadowContrastScale=0.8

[/Script/WindowsTargetPlatform.WindowsTargetSettings]
DefaultGraphicsRHI=DefaultGraphicsRHI_DX12
DefaultGraphicsRHI=DefaultGraphicsRHI_DX12
-D3D12TargetedShaderFormats=PCD3D_SM5
+D3D12TargetedShaderFormats=PCD3D_SM6
-D3D11TargetedShaderFormats=PCD3D_SM5
+D3D11TargetedShaderFormats=PCD3D_SM5
Compiler=Default
AudioSampleRate=48000
AudioCallbackBufferFrameSize=1024
AudioNumBuffersToEnqueue=1
AudioMaxChannels=0
AudioNumSourceWorkers=4
SpatializationPlugin=
SourceDataOverridePlugin=
ReverbPlugin=
OcclusionPlugin=
CompressionOverrides=(bOverrideCompressionTimes=False,DurationThreshold=5.000000,MaxNumRandomBranches=0,SoundCueQualityIndex=0)
CacheSizeKB=65536
MaxChunkSizeOverrideKB=0
bResampleForDevice=False
MaxSampleRate=48000.000000
HighSampleRate=32000.000000
MedSampleRate=24000.000000
LowSampleRate=12000.000000
MinSampleRate=8000.000000
CompressionQualityModifier=1.000000
AutoStreamingThreshold=0.000000
SoundCueCookQualityIndex=-1

[/Script/LinuxTargetPlatform.LinuxTargetSettings]
-TargetedRHIs=SF_VULKAN_SM5
+TargetedRHIs=SF_VULKAN_SM6

[/Script/HardwareTargeting.HardwareTargetingSettings]
TargetedHardwareClass=Desktop
AppliedTargetedHardwareClass=Desktop
DefaultGraphicsPerformance=Maximum
AppliedDefaultGraphicsPerformance=Maximum

[/Script/Engine.Engine]
+ActiveGameNameRedirects=(OldGameName="TP_TopDown",NewGameName="/Script/WuXia")
+ActiveGameNameRedirects=(OldGameName="/Script/TP_TopDown",NewGameName="/Script/WuXia")
+ActiveClassRedirects=(OldClassName="TP_TopDownPlayerController",NewClassName="WuXiaPlayerController")
+ActiveClassRedirects=(OldClassName="TP_TopDownGameMode",NewClassName="WuXiaGameMode")
+ActiveClassRedirects=(OldClassName="TP_TopDownCharacter",NewClassName="WuXiaCharacter")

[/Script/AndroidFileServerEditor.AndroidFileServerRuntimeSettings]
bEnablePlugin=True
bAllowNetworkConnection=True
SecurityToken=AA14FFBB45D606E9ED7F6E9C6DBB45B8
bIncludeInShipping=False
bAllowExternalStartInShipping=False
bCompileAFSProject=False
bUseCompression=False
bLogFiles=False
bReportStats=False
ConnectionType=USBOnly
bUseManualIPAddress=False
ManualIPAddress=


[CoreRedirects]
+ClassRedirects=(OldName="/Script/WuXia.GameDiceCheckSystem",NewName="/Script/WuXia.WX_GameDiceCheckSystem")
+ClassRedirects=(OldName="/Script/WuXia.GameDialogSystem",NewName="/Script/WuXia.WX_GameDialogSystem")
+ClassRedirects=(OldName="/Script/WuXia.WX_BattleMagComponent",NewName="/Script/WuXia.WX_BattleMag")
+ClassRedirects=(OldName="/Script/WuXia.GameStateManager",NewName="/Script/WuXia.GameModeManager")